package main

import (
	"fmt"
	"log"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"scanner/internal/models"
)

func main() {
	fmt.Println("🔍 检查任务327的信息收集数据")

	// 连接数据库
	db, err := gorm.Open(sqlite.Open("data/scanner.db"), &gorm.Config{})
	if err != nil {
		log.Fatal("连接数据库失败:", err)
	}

	// 查询任务327
	var task models.ScanTask
	if err := db.Where("id = ?", 327).First(&task).Error; err != nil {
		log.Fatal("查询任务327失败:", err)
	}

	fmt.Printf("📋 任务327基本信息:\n")
	fmt.Printf("  ID: %d\n", task.ID)
	fmt.Printf("  名称: %s\n", task.Name)
	fmt.Printf("  状态: %s\n", task.Status)
	fmt.Printf("  进度: %d%%\n", task.Progress)

	fmt.Printf("\n🔧 信息收集数据:\n")
	if task.InfoGatheringData != "" {
		fmt.Printf("  ✅ 数据长度: %d 字符\n", len(task.InfoGatheringData))
		fmt.Printf("  数据内容: %s\n", task.InfoGatheringData)
	} else {
		fmt.Printf("  ❌ 信息收集数据为空\n")
	}

	// 检查指纹识别相关的日志
	fmt.Printf("\n🔍 指纹识别相关日志:\n")
	var fingerprintLogs []models.ScanLog
	if err := db.Where("task_id = ? AND (message LIKE '%指纹识别%' OR message LIKE '%PHP%' OR message LIKE '%Nginx%')", 327).Order("created_at ASC").Find(&fingerprintLogs).Error; err != nil {
		fmt.Printf("  ❌ 查询指纹识别日志失败: %v\n", err)
	} else {
		fmt.Printf("  找到 %d 条相关日志:\n", len(fingerprintLogs))
		for i, log := range fingerprintLogs {
			fmt.Printf("    %d. [%s] %s: %s\n", i+1, log.Level, log.Stage, log.Message)
		}
	}

	// 检查信息收集数据保存相关的日志
	fmt.Printf("\n💾 信息收集数据保存日志:\n")
	var saveDataLogs []models.ScanLog
	if err := db.Where("task_id = ? AND message LIKE '%信息收集数据%'", 327).Order("created_at ASC").Find(&saveDataLogs).Error; err != nil {
		fmt.Printf("  ❌ 查询保存日志失败: %v\n", err)
	} else {
		if len(saveDataLogs) > 0 {
			fmt.Printf("  找到 %d 条保存日志:\n", len(saveDataLogs))
			for i, log := range saveDataLogs {
				fmt.Printf("    %d. [%s] %s: %s\n", i+1, log.Level, log.Stage, log.Message)
			}
		} else {
			fmt.Printf("  ❌ 未找到信息收集数据保存日志\n")
		}
	}

	fmt.Printf("\n📋 总结:\n")
	if task.InfoGatheringData != "" {
		fmt.Printf("✅ 信息收集数据已保存到数据库\n")
	} else {
		fmt.Printf("❌ 信息收集数据未保存到数据库\n")
	}
	
	if len(fingerprintLogs) > 0 {
		fmt.Printf("✅ 指纹识别正常执行，检测到技术栈信息\n")
	} else {
		fmt.Printf("❌ 指纹识别未执行或未检测到技术栈\n")
	}
}
