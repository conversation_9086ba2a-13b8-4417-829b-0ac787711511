#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查信息收集数据脚本
查看数据库中存储的信息收集数据
"""

import sqlite3

def main():
    # 连接数据库
    conn = sqlite3.connect('data/scanner.db')
    cursor = conn.cursor()

    print('=== 信息收集数据统计 ===')
    # 统计不同类型的信息收集数据
    cursor.execute('SELECT info_type, category, COUNT(*) as count FROM info_gathering_logs GROUP BY info_type, category ORDER BY count DESC')
    stats = cursor.fetchall()

    for stat in stats:
        print(f'{stat[0]} - {stat[1]}: {stat[2]} 条记录')

    print('\n=== 各类信息收集详情 ===')

    # 查看指纹识别数据
    print('\n1. 指纹识别 (fingerprint):')
    cursor.execute('SELECT category, name, value, confidence FROM info_gathering_logs WHERE info_type = "fingerprint" ORDER BY confidence DESC')
    fingerprints = cursor.fetchall()
    for fp in fingerprints:
        confidence = fp[3] if fp[3] is not None else 0.0
        print(f'  {fp[0]} - {fp[1]}: {fp[2]} (置信度: {confidence:.2f})')

    # 查看目录发现数据
    print('\n2. 目录发现 (directory):')
    cursor.execute('SELECT DISTINCT value FROM info_gathering_logs WHERE info_type = "directory"')
    directories = cursor.fetchall()
    for dir in directories:
        print(f'  发现目录: {dir[0]}')

    # 查看服务识别数据
    print('\n3. 服务识别 (service):')
    cursor.execute('SELECT category, name, value FROM info_gathering_logs WHERE info_type = "service"')
    services = cursor.fetchall()
    for svc in services:
        print(f'  {svc[0]} - {svc[1]}: {svc[2]}')

    # 查看技术栈数据
    print('\n4. 技术栈 (technology):')
    cursor.execute('SELECT category, name, value FROM info_gathering_logs WHERE info_type = "technology"')
    technologies = cursor.fetchall()
    for tech in technologies:
        print(f'  {tech[0]} - {tech[1]}: {tech[2]}')

    print('\n=== 最新的信息收集记录 ===')
    cursor.execute('SELECT * FROM info_gathering_logs ORDER BY created_at DESC LIMIT 10')
    recent_logs = cursor.fetchall()
    
    for log in recent_logs:
        print(f'\nID: {log[0]}, TaskID: {log[1]}')
        print(f'Target: {log[2]}')
        print(f'Type: {log[3]}, Category: {log[4]}')
        print(f'Name: {log[5]}, Value: {log[6]}')
        print(f'Confidence: {log[7]}, StatusCode: {log[8]}')
        evidence = log[9]
        if evidence and len(str(evidence)) > 100:
            print(f'Evidence: {str(evidence)[:100]}...')
        else:
            print(f'Evidence: {evidence}')
        print(f'Created: {log[11]}')

    conn.close()

if __name__ == "__main__":
    main()
