package engines

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"sync"
	"time"

	"scanner/internal/services"
	"scanner/pkg/logger"
)

// EnhancedDirectoryScanner 增强目录扫描引擎
// 集成信息收集服务，将发现的目录信息保存到数据库
type EnhancedDirectoryScanner struct {
	client               *http.Client
	infoGatheringService *services.InfoGatheringService
	scanLogService       *services.ScanLogService
	config               *DirectoryScanConfig
}

// DirectoryScanConfig 目录扫描配置
type DirectoryScanConfig struct {
	MaxConcurrency int           `json:"max_concurrency"` // 最大并发数
	RequestTimeout time.Duration `json:"request_timeout"` // 请求超时
	RequestDelay   time.Duration `json:"request_delay"`   // 请求间隔
	UserAgent      string        `json:"user_agent"`      // User-Agent
	FollowRedirect bool          `json:"follow_redirect"` // 是否跟随重定向
}

// DirectoryResult 目录扫描结果
type DirectoryResult struct {
	Path       string `json:"path"`        // 路径
	StatusCode int    `json:"status_code"` // 状态码
	Size       int64  `json:"size"`        // 响应大小
	Title      string `json:"title"`       // 页面标题
	Server     string `json:"server"`      // 服务器信息
	Evidence   string `json:"evidence"`    // 证据信息
}

// NewEnhancedDirectoryScanner 创建增强目录扫描引擎
func NewEnhancedDirectoryScanner(infoGatheringService *services.InfoGatheringService, scanLogService *services.ScanLogService) *EnhancedDirectoryScanner {
	config := &DirectoryScanConfig{
		MaxConcurrency: 10,
		RequestTimeout: 10 * time.Second,
		RequestDelay:   200 * time.Millisecond,
		UserAgent:      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
		FollowRedirect: false,
	}

	client := &http.Client{
		Timeout: config.RequestTimeout,
	}

	// 配置不跟随重定向
	if !config.FollowRedirect {
		client.CheckRedirect = func(req *http.Request, via []*http.Request) error {
			return http.ErrUseLastResponse
		}
	}

	return &EnhancedDirectoryScanner{
		client:               client,
		infoGatheringService: infoGatheringService,
		scanLogService:       scanLogService,
		config:               config,
	}
}

// ScanDirectories 扫描目录并记录到数据库
func (e *EnhancedDirectoryScanner) ScanDirectories(ctx context.Context, taskID uint, targetURL string) ([]*DirectoryResult, error) {
	logger.Infof("开始目录扫描: %s", targetURL)

	// 记录扫描日志
	if e.scanLogService != nil {
		e.scanLogService.LogInfo(taskID, "目录扫描", targetURL, "开始目录扫描", 0)
	}

	// 解析目标URL
	baseURL, err := url.Parse(targetURL)
	if err != nil {
		return nil, fmt.Errorf("解析URL失败: %v", err)
	}

	// 获取常见目录列表
	directories := e.getCommonDirectories()

	// 记录扫描目录列表
	if e.scanLogService != nil {
		e.scanLogService.LogDebug(taskID, "目录扫描", targetURL,
			fmt.Sprintf("扫描目录列表: %v", directories), "", 10)
	}

	var results []*DirectoryResult
	var mutex sync.Mutex
	var wg sync.WaitGroup

	// 创建并发控制信号量
	semaphore := make(chan struct{}, e.config.MaxConcurrency)

	for i, directory := range directories {
		select {
		case <-ctx.Done():
			return results, ctx.Err()
		case semaphore <- struct{}{}:
		}

		wg.Add(1)
		go func(dir string, index int) {
			defer func() {
				<-semaphore
				wg.Done()
			}()

			// 构建目录URL
			dirURL := fmt.Sprintf("%s://%s%s", baseURL.Scheme, baseURL.Host, dir)

			// 扫描目录
			if result := e.scanSingleDirectory(ctx, taskID, dirURL, dir); result != nil {
				mutex.Lock()
				results = append(results, result)
				mutex.Unlock()

				// 记录发现的目录到数据库
				if e.infoGatheringService != nil {
					evidence := fmt.Sprintf("HTTP状态码: %d, 响应大小: %d bytes", result.StatusCode, result.Size)
					if result.Title != "" {
						evidence += fmt.Sprintf(", 页面标题: %s", result.Title)
					}
					if result.Server != "" {
						evidence += fmt.Sprintf(", 服务器: %s", result.Server)
					}

					e.infoGatheringService.LogDirectory(taskID, targetURL, dir, result.StatusCode, evidence)
				}

				// 记录扫描日志
				if e.scanLogService != nil {
					message := fmt.Sprintf("发现可访问目录: %s [状态码: %d]", dir, result.StatusCode)
					e.scanLogService.LogInfo(taskID, "目录扫描", dirURL, message, 0)
				}
			}

			// 计算进度
			progress := int((float64(index+1) / float64(len(directories))) * 80) + 10 // 10-90%

			// 添加请求延迟
			time.Sleep(e.config.RequestDelay)
		}(directory, i)
	}

	wg.Wait()

	logger.Infof("目录扫描完成，发现 %d 个可访问目录", len(results))

	// 记录完成日志
	if e.scanLogService != nil {
		e.scanLogService.LogInfo(taskID, "目录扫描", targetURL,
			fmt.Sprintf("目录扫描完成，发现 %d 个可访问目录", len(results)), 100)
	}

	return results, nil
}

// scanSingleDirectory 扫描单个目录
func (e *EnhancedDirectoryScanner) scanSingleDirectory(ctx context.Context, taskID uint, dirURL, path string) *DirectoryResult {
	req, err := http.NewRequestWithContext(ctx, "GET", dirURL, nil)
	if err != nil {
		return nil
	}

	req.Header.Set("User-Agent", e.config.UserAgent)

	resp, err := e.client.Do(req)
	if err != nil {
		return nil
	}
	defer resp.Body.Close()

	// 只处理成功的响应
	if resp.StatusCode < 200 || resp.StatusCode >= 400 {
		return nil
	}

	// 读取响应体
	body := make([]byte, 10*1024) // 只读取前10KB用于分析
	n, _ := resp.Body.Read(body)
	responseBody := string(body[:n])

	result := &DirectoryResult{
		Path:       path,
		StatusCode: resp.StatusCode,
		Size:       int64(n),
		Server:     resp.Header.Get("Server"),
	}

	// 提取页面标题
	if title := e.extractTitle(responseBody); title != "" {
		result.Title = title
	}

	// 构建证据信息
	evidence := fmt.Sprintf("HTTP %d", resp.StatusCode)
	if result.Title != "" {
		evidence += fmt.Sprintf(", 标题: %s", result.Title)
	}
	if result.Server != "" {
		evidence += fmt.Sprintf(", 服务器: %s", result.Server)
	}
	result.Evidence = evidence

	return result
}

// extractTitle 提取页面标题
func (e *EnhancedDirectoryScanner) extractTitle(body string) string {
	// 简单的标题提取
	start := strings.Index(strings.ToLower(body), "<title")
	if start == -1 {
		return ""
	}

	start = strings.Index(body[start:], ">")
	if start == -1 {
		return ""
	}
	start += strings.Index(body[:start], "<title")

	end := strings.Index(strings.ToLower(body[start:]), "</title>")
	if end == -1 {
		return ""
	}

	title := body[start+1 : start+end]
	title = strings.TrimSpace(title)

	// 限制标题长度
	if len(title) > 100 {
		title = title[:100] + "..."
	}

	return title
}

// getCommonDirectories 获取常见目录列表
func (e *EnhancedDirectoryScanner) getCommonDirectories() []string {
	return []string{
		"/admin",
		"/administrator",
		"/login",
		"/wp-admin",
		"/phpmyadmin",
		"/backup",
		"/backups",
		"/test",
		"/tests",
		"/dev",
		"/development",
		"/api",
		"/apis",
		"/config",
		"/configuration",
		"/upload",
		"/uploads",
		"/files",
		"/file",
		"/download",
		"/downloads",
		"/temp",
		"/tmp",
		"/cache",
		"/log",
		"/logs",
		"/debug",
		"/info",
		"/status",
		"/health",
		"/metrics",
		"/stats",
		"/statistics",
		"/dashboard",
		"/panel",
		"/control",
		"/manage",
		"/management",
		"/console",
		"/system",
		"/sys",
		"/server",
		"/service",
		"/services",
		"/app",
		"/application",
		"/apps",
		"/web",
		"/www",
		"/public",
		"/private",
		"/secure",
		"/security",
		"/auth",
		"/authentication",
		"/user",
		"/users",
		"/account",
		"/accounts",
		"/profile",
		"/profiles",
		"/settings",
		"/setting",
		"/preferences",
		"/prefs",
		"/options",
		"/opt",
		"/tools",
		"/tool",
		"/utils",
		"/utilities",
		"/help",
		"/support",
		"/docs",
		"/documentation",
		"/manual",
		"/guide",
		"/readme",
		"/about",
		"/contact",
		"/feedback",
		"/report",
		"/reports",
		"/data",
		"/database",
		"/db",
		"/sql",
		"/mysql",
		"/postgres",
		"/oracle",
		"/mongo",
		"/redis",
		"/elastic",
		"/search",
		"/index",
		"/home",
		"/main",
		"/default",
		"/welcome",
		"/portal",
		"/gateway",
		"/proxy",
		"/load",
		"/balance",
		"/cluster",
		"/node",
		"/nodes",
		"/worker",
		"/workers",
		"/queue",
		"/queues",
		"/job",
		"/jobs",
		"/task",
		"/tasks",
		"/schedule",
		"/scheduler",
		"/cron",
		"/batch",
		"/process",
		"/processes",
		"/monitor",
		"/monitoring",
		"/alert",
		"/alerts",
		"/notification",
		"/notifications",
		"/mail",
		"/email",
		"/message",
		"/messages",
		"/chat",
		"/forum",
		"/blog",
		"/news",
		"/article",
		"/articles",
		"/post",
		"/posts",
		"/page",
		"/pages",
		"/content",
		"/media",
		"/image",
		"/images",
		"/img",
		"/picture",
		"/pictures",
		"/photo",
		"/photos",
		"/video",
		"/videos",
		"/audio",
		"/music",
		"/sound",
		"/document",
		"/documents",
		"/doc",
		"/docs",
		"/pdf",
		"/excel",
		"/word",
		"/powerpoint",
		"/archive",
		"/archives",
		"/zip",
		"/tar",
		"/gz",
		"/rar",
		"/7z",
	}
}
