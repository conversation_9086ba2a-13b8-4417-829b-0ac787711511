package engines

import (
	"context"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"scanner/internal/services"
	"scanner/pkg/logger"
)

// EnhancedFingerprintEngine 增强指纹识别引擎
// 集成信息收集服务，将识别结果保存到数据库
type EnhancedFingerprintEngine struct {
	client               *http.Client
	infoGatheringService *services.InfoGatheringService
	scanLogService       *services.ScanLogService
	rules                map[string]*FingerprintRule
}

// FingerprintRule 指纹识别规则
type FingerprintRule struct {
	Name           string            `json:"name"`            // 技术名称
	Category       string            `json:"category"`        // 分类：server, framework, cms, database等
	Confidence     float64           `json:"confidence"`      // 基础置信度
	Headers        map[string]string `json:"headers"`         // HTTP头部特征
	BodyPatterns   []string          `json:"body_patterns"`   // 响应体匹配模式
	VersionPattern string            `json:"version_pattern"` // 版本提取模式
	Description    string            `json:"description"`     // 描述信息
}

// FingerprintResult 指纹识别结果
type FingerprintResult struct {
	Technology string            `json:"technology"` // 技术名称
	Category   string            `json:"category"`   // 分类
	Version    string            `json:"version"`    // 版本
	Confidence float64           `json:"confidence"` // 置信度
	Evidence   []string          `json:"evidence"`   // 证据
	Metadata   map[string]string `json:"metadata"`   // 额外信息
}

// NewEnhancedFingerprintEngine 创建增强指纹识别引擎
func NewEnhancedFingerprintEngine(infoGatheringService *services.InfoGatheringService, scanLogService *services.ScanLogService) *EnhancedFingerprintEngine {
	engine := &EnhancedFingerprintEngine{
		client: &http.Client{
			Timeout: time.Second * 10,
		},
		infoGatheringService: infoGatheringService,
		scanLogService:       scanLogService,
		rules:                make(map[string]*FingerprintRule),
	}

	// 加载指纹识别规则
	engine.loadFingerprintRules()

	return engine
}

// IdentifyTechnologies 识别技术栈并记录到数据库
func (e *EnhancedFingerprintEngine) IdentifyTechnologies(ctx context.Context, taskID uint, targetURL string) ([]*FingerprintResult, error) {
	logger.Infof("开始指纹识别: %s", targetURL)

	// 记录扫描日志
	if e.scanLogService != nil {
		e.scanLogService.LogInfo(taskID, "指纹识别", targetURL, "开始指纹识别", 0)
	}

	// 发送HTTP请求获取响应信息
	req, err := http.NewRequestWithContext(ctx, "GET", targetURL, nil)
	if err != nil {
		return nil, err
	}

	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")

	resp, err := e.client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// 读取响应体
	body := make([]byte, 50*1024) // 只读取前50KB用于指纹识别
	n, _ := resp.Body.Read(body)
	responseBody := string(body[:n])

	var results []*FingerprintResult

	// 遍历所有指纹规则进行匹配
	for _, rule := range e.rules {
		if result := e.matchRule(rule, resp, responseBody); result != nil {
			results = append(results, result)

			// 记录指纹识别结果到数据库
			if e.infoGatheringService != nil {
				evidence := strings.Join(result.Evidence, "; ")
				e.infoGatheringService.LogFingerprint(
					taskID,
					targetURL,
					result.Category,
					result.Technology,
					result.Version,
					result.Confidence,
					evidence,
				)
			}

			// 记录扫描日志
			if e.scanLogService != nil {
				message := fmt.Sprintf("检测到%s: %s", e.getCategoryDisplayName(result.Category), result.Technology)
				if result.Version != "" {
					message += fmt.Sprintf(" %s", result.Version)
				}
				e.scanLogService.LogInfo(taskID, "指纹识别", targetURL, message, 0)
			}
		}
	}

	logger.Infof("指纹识别完成，识别到 %d 个技术", len(results))

	// 记录完成日志
	if e.scanLogService != nil {
		e.scanLogService.LogInfo(taskID, "指纹识别", targetURL,
			fmt.Sprintf("指纹识别完成，识别到 %d 个技术", len(results)), 100)
	}

	return results, nil
}

// matchRule 匹配指纹规则
func (e *EnhancedFingerprintEngine) matchRule(rule *FingerprintRule, resp *http.Response, body string) *FingerprintResult {
	var evidence []string
	var confidence float64
	var version string

	// 检查HTTP头部特征
	for headerName, expectedValue := range rule.Headers {
		if actualValue := resp.Header.Get(headerName); actualValue != "" {
			if strings.Contains(strings.ToLower(actualValue), strings.ToLower(expectedValue)) {
				evidence = append(evidence, fmt.Sprintf("Header: %s = %s", headerName, actualValue))
				confidence += 0.4

				// 尝试提取版本信息
				if rule.VersionPattern != "" {
					if v := e.extractVersion(actualValue, rule.VersionPattern); v != "" {
						version = v
					}
				}
			}
		}
	}

	// 检查响应体模式匹配
	for _, pattern := range rule.BodyPatterns {
		if matched, _ := regexp.MatchString(pattern, body); matched {
			evidence = append(evidence, fmt.Sprintf("Body pattern: %s", pattern))
			confidence += 0.3

			// 尝试提取版本信息
			if rule.VersionPattern != "" && version == "" {
				if v := e.extractVersion(body, rule.VersionPattern); v != "" {
					version = v
				}
			}
		}
	}

	// 如果置信度足够高，返回结果
	if confidence >= 0.3 {
		finalConfidence := minFloat64(confidence*rule.Confidence, 1.0)
		return &FingerprintResult{
			Technology: rule.Name,
			Category:   rule.Category,
			Version:    version,
			Confidence: finalConfidence,
			Evidence:   evidence,
			Metadata: map[string]string{
				"rule_confidence": fmt.Sprintf("%.2f", rule.Confidence),
				"description":     rule.Description,
			},
		}
	}

	return nil
}

// extractVersion 从字符串中提取版本信息
func (e *EnhancedFingerprintEngine) extractVersion(text, versionPattern string) string {
	if versionPattern == "" {
		return ""
	}

	re, err := regexp.Compile(versionPattern)
	if err != nil {
		return ""
	}

	matches := re.FindStringSubmatch(text)
	if len(matches) > 1 {
		return matches[1]
	}

	return ""
}

// getCategoryDisplayName 获取分类的显示名称
func (e *EnhancedFingerprintEngine) getCategoryDisplayName(category string) string {
	displayNames := map[string]string{
		"server":    "服务器",
		"framework": "框架",
		"cms":       "内容管理系统",
		"database":  "数据库",
		"language":  "编程语言",
		"library":   "库",
		"os":        "操作系统",
		"cdn":       "CDN",
		"waf":       "防火墙",
	}

	if displayName, exists := displayNames[category]; exists {
		return displayName
	}
	return category
}

// loadFingerprintRules 加载指纹识别规则
func (e *EnhancedFingerprintEngine) loadFingerprintRules() {
	// Web服务器指纹
	e.rules["nginx"] = &FingerprintRule{
		Name:           "Nginx",
		Category:       "server",
		Confidence:     0.9,
		Headers:        map[string]string{"Server": "nginx"},
		BodyPatterns:   []string{},
		VersionPattern: `nginx/([0-9.]+)`,
		Description:    "Nginx Web服务器",
	}

	e.rules["apache"] = &FingerprintRule{
		Name:           "Apache",
		Category:       "server",
		Confidence:     0.9,
		Headers:        map[string]string{"Server": "Apache"},
		BodyPatterns:   []string{},
		VersionPattern: `Apache/([0-9.]+)`,
		Description:    "Apache HTTP服务器",
	}

	e.rules["openresty"] = &FingerprintRule{
		Name:           "OpenResty",
		Category:       "server",
		Confidence:     0.9,
		Headers:        map[string]string{"Server": "openresty"},
		BodyPatterns:   []string{},
		VersionPattern: `openresty/([0-9.]+)`,
		Description:    "OpenResty Web平台",
	}

	// 编程语言和框架指纹
	e.rules["php"] = &FingerprintRule{
		Name:           "PHP",
		Category:       "language",
		Confidence:     0.8,
		Headers:        map[string]string{"X-Powered-By": "PHP"},
		BodyPatterns:   []string{`\.php`, `\?php`},
		VersionPattern: `PHP/([0-9.]+)`,
		Description:    "PHP编程语言",
	}

	e.rules["asp_net"] = &FingerprintRule{
		Name:           "ASP.NET",
		Category:       "framework",
		Confidence:     0.8,
		Headers:        map[string]string{"X-AspNet-Version": ""},
		BodyPatterns:   []string{`__VIEWSTATE`, `aspx`},
		VersionPattern: `([0-9.]+)`,
		Description:    "ASP.NET Web框架",
	}

	logger.Infof("已加载 %d 条指纹识别规则", len(e.rules))
}

// minFloat64 返回两个float64中的较小值
func minFloat64(a, b float64) float64 {
	if a < b {
		return a
	}
	return b
}
