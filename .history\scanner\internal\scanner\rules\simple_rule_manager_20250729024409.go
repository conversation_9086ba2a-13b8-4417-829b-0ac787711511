package rules

import (
	"context"
	"fmt"
	"net/http"
	"sync"
	"time"

	"scanner/internal/services"
	"scanner/pkg/logger"
)

// SimpleRuleManager 简化的规则管理器
// 负责基础的规则匹配和漏洞检测
type SimpleRuleManager struct {
	// 规则引擎
	ruleEngine *RuleEngine
	
	// 配置
	rulesDir string
	enabled  bool
	
	// 同步
	mutex sync.RWMutex
	
	// 服务依赖
	vulnService *services.VulnerabilityService
	logService  *services.ScanLogService
	
	// 统计
	stats *SimpleRuleStats
}

// SimpleRuleStats 简化的规则统计
type SimpleRuleStats struct {
	TotalRules    int64     `json:"total_rules"`
	MatchedRules  int64     `json:"matched_rules"`
	LastUpdate    time.Time `json:"last_update"`
	LastRuleMatch time.Time `json:"last_rule_match"`
}

// SimpleRuleMatchResult 简化的规则匹配结果
type SimpleRuleMatchResult struct {
	RuleID     string    `json:"rule_id"`
	RuleName   string    `json:"rule_name"`
	RuleType   string    `json:"rule_type"`
	Severity   string    `json:"severity"`
	Confidence float64   `json:"confidence"`
	Matched    bool      `json:"matched"`
	Evidence   string    `json:"evidence"`
	MatchTime  time.Time `json:"match_time"`
	Target     string    `json:"target"`
}

// NewSimpleRuleManager 创建简化的规则管理器
func NewSimpleRuleManager(rulesDir string, vulnService *services.VulnerabilityService, logService *services.ScanLogService) *SimpleRuleManager {
	manager := &SimpleRuleManager{
		rulesDir:    rulesDir,
		enabled:     true,
		vulnService: vulnService,
		logService:  logService,
		stats: &SimpleRuleStats{
			LastUpdate: time.Now(),
		},
	}
	
	// 初始化规则引擎
	manager.ruleEngine = NewRuleEngine()
	
	// 加载规则
	if err := manager.LoadRules(); err != nil {
		logger.Errorf("加载规则失败: %v", err)
	}
	
	return manager
}

// LoadRules 加载规则
func (rm *SimpleRuleManager) LoadRules() error {
	rm.mutex.Lock()
	defer rm.mutex.Unlock()
	
	logger.Info("开始加载检测规则...")
	
	// 加载规则目录中的所有规则
	if err := rm.ruleEngine.LoadRulesFromDirectory(rm.rulesDir); err != nil {
		logger.Errorf("加载规则失败: %v", err)
		return err
	}
	
	// 更新统计
	rm.updateStats()
	
	logger.Infof("规则加载完成 - 总规则数: %d", rm.stats.TotalRules)
	
	return nil
}

// MatchWebRules 匹配Web规则
func (rm *SimpleRuleManager) MatchWebRules(ctx context.Context, target string, request *http.Request, response *http.Response) ([]*SimpleRuleMatchResult, error) {
	if !rm.enabled {
		return nil, nil
	}
	
	rm.mutex.RLock()
	defer rm.mutex.RUnlock()
	
	var results []*SimpleRuleMatchResult
	
	// 使用规则引擎进行匹配
	matches := rm.ruleEngine.MatchRule(target, response)
	
	for _, match := range matches {
		result := &SimpleRuleMatchResult{
			RuleID:     match.Rule.ID,
			RuleName:   match.Rule.Name,
			RuleType:   match.Rule.Type,
			Severity:   match.Rule.Severity,
			Confidence: match.Confidence,
			Matched:    true,
			Evidence:   match.Evidence,
			MatchTime:  time.Now(),
			Target:     target,
		}
		
		results = append(results, result)
		
		// 记录匹配日志
		if rm.logService != nil {
			rm.logService.LogInfo(0, "规则匹配", target, 
				fmt.Sprintf("匹配到规则: %s (%s)", match.Rule.Name, match.Rule.Severity), 0)
		}
	}
	
	// 更新统计
	rm.stats.MatchedRules += int64(len(results))
	rm.stats.LastRuleMatch = time.Now()
	
	return results, nil
}

// CreateVulnerabilityFromRule 从规则匹配结果创建漏洞
func (rm *SimpleRuleManager) CreateVulnerabilityFromRule(taskID uint, result *SimpleRuleMatchResult) error {
	if rm.vulnService == nil {
		return fmt.Errorf("漏洞服务未初始化")
	}
	
	// 构建漏洞信息
	vulnData := map[string]interface{}{
		"task_id":     taskID,
		"target":      result.Target,
		"name":        result.RuleName,
		"description": fmt.Sprintf("规则匹配检测到的漏洞: %s", result.RuleName),
		"severity":    result.Severity,
		"confidence":  result.Confidence,
		"evidence":    result.Evidence,
		"solution":    "请根据漏洞类型采取相应的修复措施",
		"references":  "",
		"tags":        []string{result.RuleType, "rule-based"},
		"metadata": map[string]interface{}{
			"rule_id":    result.RuleID,
			"rule_type":  result.RuleType,
			"match_time": result.MatchTime,
		},
		"status":     "open",
		"created_at": time.Now(),
		"updated_at": time.Now(),
	}
	
	// 使用通用的创建方法
	return rm.vulnService.CreateVulnerabilityFromData(vulnData)
}

// updateStats 更新统计信息
func (rm *SimpleRuleManager) updateStats() {
	rm.stats.TotalRules = int64(len(rm.ruleEngine.GetAllRules()))
	rm.stats.LastUpdate = time.Now()
}

// GetStats 获取统计信息
func (rm *SimpleRuleManager) GetStats() *SimpleRuleStats {
	rm.mutex.RLock()
	defer rm.mutex.RUnlock()
	
	// 更新统计
	rm.updateStats()
	
	return rm.stats
}

// IsEnabled 检查是否启用
func (rm *SimpleRuleManager) IsEnabled() bool {
	rm.mutex.RLock()
	defer rm.mutex.RUnlock()
	return rm.enabled
}

// SetEnabled 设置启用状态
func (rm *SimpleRuleManager) SetEnabled(enabled bool) {
	rm.mutex.Lock()
	defer rm.mutex.Unlock()
	rm.enabled = enabled
	
	logger.Infof("规则管理器状态更新: %v", enabled)
}

// ReloadRules 重新加载规则
func (rm *SimpleRuleManager) ReloadRules() error {
	logger.Info("重新加载检测规则...")
	return rm.LoadRules()
}

// GetRuleEngine 获取规则引擎
func (rm *SimpleRuleManager) GetRuleEngine() *RuleEngine {
	return rm.ruleEngine
}
