package models

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"

	"gorm.io/gorm"
)

// UintSlice 自定义uint切片类型，用于JSON序列化
type UintSlice []uint

// Value 实现driver.Valuer接口，用于数据库存储
func (us UintSlice) Value() (driver.Value, error) {
	if len(us) == 0 {
		return "[]", nil
	}
	data, err := json.Marshal(us)
	return string(data), err
}

// Scan 实现sql.Scanner接口，用于数据库读取
func (us *UintSlice) Scan(value interface{}) error {
	if value == nil {
		*us = UintSlice{}
		return nil
	}

	var data []byte
	switch v := value.(type) {
	case string:
		data = []byte(v)
	case []byte:
		data = v
	case int64:
		// 处理SQLite返回的单个数字值
		*us = UintSlice{uint(v)}
		return nil
	default:
		return fmt.Errorf("无法将 %T 转换为 UintSlice", value)
	}

	if len(data) == 0 || string(data) == "null" {
		*us = UintSlice{}
		return nil
	}

	return json.Unmarshal(data, us)
}

// User 用户模型
type User struct {
	ID           uint           `gorm:"primarykey" json:"id"`
	Username     string         `gorm:"uniqueIndex;size:50;not null" json:"username"`
	Email        string         `gorm:"uniqueIndex;size:100" json:"email"`
	Phone        string         `gorm:"size:20" json:"phone"` // 手机号
	PasswordHash string         `gorm:"size:255;not null" json:"-"`
	Role         string         `gorm:"size:20;default:operator" json:"role"` // admin, auditor, operator
	Status       string         `gorm:"size:20;default:active" json:"status"` // active, inactive
	LastLogin    *time.Time     `json:"last_login"`
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `gorm:"index" json:"-"`
}

// TableName 指定表名
func (User) TableName() string {
	return "users"
}

// Asset 资产模型
type Asset struct {
	ID          uint       `gorm:"primarykey" json:"id"`
	Name        string     `gorm:"size:100;not null" json:"name"`
	Type        string     `gorm:"size:20;not null;index" json:"type"` // web, host, network, database
	IP          string     `gorm:"size:45" json:"ip"`
	Domain      string     `gorm:"size:255" json:"domain"`
	Port        int        `json:"port"`
	Protocol    string     `gorm:"size:10" json:"protocol"`
	Tags        string     `gorm:"size:500" json:"tags"`
	Owner       string     `gorm:"size:50" json:"owner"`
	Status      string     `gorm:"size:20;default:active;index" json:"status"`
	Description string     `gorm:"type:text" json:"description"` // 资产描述
	LastScan    *time.Time `json:"lastScan"`
	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"updatedAt"`
	// 移除 DeletedAt 字段，改为物理删除

	// 关联关系
	ScanTasks       []ScanTask      `gorm:"foreignKey:AssetID" json:"scan_tasks,omitempty"`
	Vulnerabilities []Vulnerability `gorm:"foreignKey:AssetID" json:"vulnerabilities,omitempty"`
}

// TableName 指定表名
func (Asset) TableName() string {
	return "assets"
}

// ScanTask 扫描任务模型
type ScanTask struct {
	ID          uint   `gorm:"primarykey" json:"id"`
	Name        string `gorm:"size:100;not null" json:"name"`               // 任务名称
	Type        string `gorm:"size:20;not null;index" json:"type"`          // 扫描类型: web, network, host, api, compliance, discovery
	Status      string `gorm:"size:20;default:pending;index" json:"status"` // 任务状态: pending, running, completed, failed, stopped
	Priority    int    `gorm:"default:5" json:"priority"`                   // 优先级 1-10
	Progress    int    `gorm:"default:0" json:"progress"`                   // 进度百分比 0-100
	Description string `gorm:"type:text" json:"description"`                // 任务描述

	// 目标配置
	Targets string `gorm:"type:text;not null" json:"targets"` // 扫描目标，JSON格式
	AssetID *uint  `gorm:"index" json:"asset_id,omitempty"`   // 关联资产ID（可空）

	// 扫描配置
	Config  string `gorm:"type:text" json:"config"`     // 扫描配置，JSON格式
	Depth   int    `gorm:"default:2" json:"depth"`      // 扫描深度 1-浅度 2-中度 3-深度
	Timeout int    `gorm:"default:3600" json:"timeout"` // 超时时间（秒）
	Threads int    `gorm:"default:5" json:"threads"`    // 并发线程数

	// 调度配置
	ScheduleType string     `gorm:"size:20;default:immediate" json:"schedule_type"` // 调度类型: immediate, scheduled, recurring
	ScheduledAt  *time.Time `json:"scheduled_at"`                                   // 计划执行时间
	CronExpr     string     `gorm:"size:100" json:"cron_expr"`                      // Cron表达式（周期任务）

	// 时间信息
	StartTime *time.Time `json:"start_time"` // 开始时间
	EndTime   *time.Time `json:"end_time"`   // 结束时间
	Duration  int        `json:"duration"`   // 执行时长（秒）

	// 结果统计
	VulnCount     int `gorm:"default:0" json:"vuln_count"`     // 漏洞总数
	CriticalCount int `gorm:"default:0" json:"critical_count"` // 严重漏洞数
	HighCount     int `gorm:"default:0" json:"high_count"`     // 高危漏洞数
	MediumCount   int `gorm:"default:0" json:"medium_count"`   // 中危漏洞数
	LowCount      int `gorm:"default:0" json:"low_count"`      // 低危漏洞数
	InfoCount     int `gorm:"default:0" json:"info_count"`     // 信息漏洞数

	// 扫描统计
	TotalTargets    int `gorm:"default:0" json:"total_targets"`    // 总目标数
	ScannedTargets  int `gorm:"default:0" json:"scanned_targets"`  // 已扫描目标数
	TotalRequests   int `gorm:"default:0" json:"total_requests"`   // 总请求数
	SuccessRequests int `gorm:"default:0" json:"success_requests"` // 成功请求数
	FailedRequests  int `gorm:"default:0" json:"failed_requests"`  // 失败请求数

	// 错误信息
	ErrorMessage string `gorm:"type:text" json:"error_message"` // 错误信息

	// 信息收集结果
	InfoGatheringData string `gorm:"type:text" json:"info_gathering_data"` // 信息收集数据（JSON格式）

	// 审计字段
	CreatedBy *uint          `gorm:"index" json:"created_by,omitempty"` // 创建者ID（可空）
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`

	// 关联关系（暂时移除外键约束，使用gorm:"-"忽略关联）
	Asset           *Asset           `gorm:"-" json:"asset,omitempty"`
	Creator         *User            `gorm:"-" json:"creator,omitempty"`
	Vulnerabilities []*Vulnerability `gorm:"-" json:"vulnerabilities,omitempty"`
}

// TableName 设置表名
func (ScanTask) TableName() string {
	return "scan_tasks"
}

// Vulnerability 漏洞模型
type Vulnerability struct {
	ID         uint  `gorm:"primarykey" json:"id"`
	ScanTaskID *uint `gorm:"index" json:"scan_task_id"`     // 扫描任务ID（旧字段，兼容性）
	TaskID     uint  `gorm:"index;not null" json:"task_id"` // 关联扫描任务ID
	AssetID    *uint `gorm:"index" json:"asset_id"`         // 关联资产ID

	// 漏洞基本信息
	Name        string `gorm:"size:200;not null" json:"name"`      // 漏洞名称
	Type        string `gorm:"size:50;not null;index" json:"type"` // 漏洞类型
	Title       string `gorm:"size:200;not null" json:"title"`     // 漏洞标题
	Category    string `gorm:"size:50;index" json:"category"`      // 漏洞分类
	Description string `gorm:"type:text" json:"description"`       // 漏洞描述

	// 风险评级
	Severity string  `gorm:"size:20;not null;index" json:"severity"` // 严重程度: critical, high, medium, low, info
	CVSS     float64 `gorm:"type:decimal(3,1)" json:"cvss"`          // CVSS评分
	CVE      string  `gorm:"size:20" json:"cve"`                     // CVE编号
	CWE      string  `gorm:"size:20" json:"cwe"`                     // CWE编号

	// 位置信息
	URL       string `gorm:"size:500" json:"url"`       // 漏洞URL
	Method    string `gorm:"size:10" json:"method"`     // HTTP方法
	Parameter string `gorm:"size:100" json:"parameter"` // 参数名
	Path      string `gorm:"size:500" json:"path"`      // 文件路径
	Port      int    `json:"port"`                      // 端口号

	// 攻击信息
	Payload  string `gorm:"type:text" json:"payload"`  // 攻击载荷
	Evidence string `gorm:"type:text" json:"evidence"` // 漏洞证据
	Request  string `gorm:"type:text" json:"request"`  // 请求内容
	Response string `gorm:"type:text" json:"response"` // 响应内容

	// 修复建议
	Solution   string `gorm:"type:text" json:"solution"`   // 解决方案
	References string `gorm:"type:text" json:"references"` // 参考链接，JSON格式

	// 状态管理
	Status     string     `gorm:"size:20;default:open;index" json:"status"` // 状态: open, fixed, ignored, false_positive
	FixedAt    *time.Time `json:"fixed_at"`                                 // 修复时间
	VerifiedAt *time.Time `json:"verified_at"`                              // 验证时间

	// 额外信息
	Metadata string `gorm:"type:text" json:"metadata"` // 额外元数据，JSON格式

	// 审计字段
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`

	// 关联关系
	Task  *ScanTask `gorm:"foreignKey:TaskID" json:"task,omitempty"`
	Asset *Asset    `gorm:"foreignKey:AssetID" json:"asset,omitempty"`
}

// TableName 设置表名
func (Vulnerability) TableName() string {
	return "vulnerabilities"
}

// ScanProgress 扫描进度模型
type ScanProgress struct {
	ID          uint   `gorm:"primarykey" json:"id"`
	TaskID      uint   `gorm:"index;not null" json:"task_id"` // 关联扫描任务ID
	Stage       string `gorm:"size:50;not null" json:"stage"` // 当前阶段
	Progress    int    `gorm:"not null" json:"progress"`      // 进度百分比 0-100
	Message     string `gorm:"size:500" json:"message"`       // 进度消息
	CurrentItem string `gorm:"size:500" json:"current_item"`  // 当前处理项
	Details     string `gorm:"type:text" json:"details"`      // 详细信息，JSON格式

	// 审计字段
	CreatedAt time.Time `json:"created_at"`

	// 关联关系
	Task *ScanTask `gorm:"foreignKey:TaskID" json:"task,omitempty"`
}

// TableName 设置表名
func (ScanProgress) TableName() string {
	return "scan_progress"
}

// Ticket 工单模型
type Ticket struct {
	ID               uint           `gorm:"primarykey" json:"id"`
	Title            string         `gorm:"size:200;not null" json:"title"`
	Description      string         `gorm:"type:text" json:"description"`
	Type             string         `gorm:"size:50;not null;index" json:"type"`           // vulnerability, compliance, maintenance, incident, other
	Priority         string         `gorm:"size:20;default:medium;index" json:"priority"` // critical, high, medium, low
	Status           string         `gorm:"size:20;default:open;index" json:"status"`     // open, in_progress, resolved, closed
	AssigneeEmail    string         `gorm:"size:100" json:"assignee_email"`               // 指派人邮箱地址
	CreatedBy        uint           `gorm:"index" json:"created_by"`
	Creator          *User          `gorm:"foreignKey:CreatedBy" json:"creator,omitempty"`
	VulnerabilityIDs UintSlice      `gorm:"type:json" json:"vulnerability_ids"` // 关联的漏洞ID列表
	AssetIDs         UintSlice      `gorm:"type:json" json:"asset_ids"`         // 关联的资产ID列表
	DueDate          *time.Time     `json:"due_date"`
	ResolvedAt       *time.Time     `json:"resolved_at"`
	ClosedAt         *time.Time     `json:"closed_at"`
	CreatedAt        time.Time      `json:"created_at"`
	UpdatedAt        time.Time      `json:"updated_at"`
	DeletedAt        gorm.DeletedAt `gorm:"index" json:"-"`
}

// TableName 指定表名
func (Ticket) TableName() string {
	return "tickets"
}

// Report 报告模型
type Report struct {
	ID         uint           `gorm:"primarykey" json:"id"`
	ScanTaskID uint           `gorm:"index" json:"scan_task_id"`
	Name       string         `gorm:"size:100;not null" json:"name"`
	Type       string         `gorm:"size:20;not null" json:"type"`   // scan, compliance, summary, ai
	Format     string         `gorm:"size:10;not null" json:"format"` // pdf, html, excel, json
	Content    string         `gorm:"type:text" json:"content"`
	FilePath   string         `gorm:"size:500" json:"file_path"`
	FileSize   int64          `json:"file_size"`
	Status     string         `gorm:"size:20;default:generating" json:"status"` // generating, completed, failed
	CreatedBy  uint           `gorm:"index" json:"created_by"`
	CreatedAt  time.Time      `json:"created_at"`
	UpdatedAt  time.Time      `json:"updated_at"`
	DeletedAt  gorm.DeletedAt `gorm:"index" json:"-"`

	// 关联关系
	ScanTask ScanTask `gorm:"foreignKey:ScanTaskID" json:"scan_task,omitempty"`
	Creator  User     `gorm:"foreignKey:CreatedBy" json:"creator,omitempty"`
}

// TableName 指定表名
func (Report) TableName() string {
	return "reports"
}

// SystemConfig 系统配置模型
type SystemConfig struct {
	ID          uint      `gorm:"primarykey" json:"id"`
	Category    string    `gorm:"size:50;not null" json:"category"`
	Key         string    `gorm:"size:100;not null" json:"key"`
	Value       string    `gorm:"type:text" json:"value"`
	Description string    `gorm:"type:text" json:"description"`
	IsEncrypted bool      `gorm:"default:false" json:"is_encrypted"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// TableName 指定表名
func (SystemConfig) TableName() string {
	return "system_configs"
}

// OperationLog 操作日志模型
type OperationLog struct {
	ID           uint      `gorm:"primarykey" json:"id"`
	UserID       uint      `gorm:"index" json:"user_id"`
	Action       string    `gorm:"size:50;not null" json:"action"`
	ResourceType string    `gorm:"size:50" json:"resource_type"`
	ResourceID   uint      `json:"resource_id"`
	Details      string    `gorm:"type:text" json:"details"`
	IPAddress    string    `gorm:"size:45" json:"ip_address"`
	UserAgent    string    `gorm:"type:text" json:"user_agent"`
	CreatedAt    time.Time `json:"created_at"`

	// 关联关系
	User User `gorm:"foreignKey:UserID" json:"user,omitempty"`
}

// TableName 指定表名
func (OperationLog) TableName() string {
	return "operation_logs"
}

// ScanLog 扫描日志模型
type ScanLog struct {
	ID        uint      `gorm:"primarykey" json:"id"`
	TaskID    uint      `gorm:"index;not null" json:"task_id"`
	Level     string    `gorm:"size:20;not null" json:"level"`     // DEBUG, INFO, WARN, ERROR
	Stage     string    `gorm:"size:50" json:"stage"`              // 扫描阶段
	Target    string    `gorm:"size:255" json:"target"`            // 扫描目标
	Message   string    `gorm:"type:text;not null" json:"message"` // 日志消息
	Details   string    `gorm:"type:text" json:"details"`          // 详细信息
	Progress  int       `gorm:"default:0" json:"progress"`         // 进度百分比
	CreatedAt time.Time `json:"created_at"`

	// 关联关系
	Task ScanTask `gorm:"foreignKey:TaskID" json:"task,omitempty"`
}

// InfoGatheringLog 信息收集日志模型
// 专门用于存储扫描过程中收集到的指纹识别、目录发现等信息
type InfoGatheringLog struct {
	ID         uint      `gorm:"primarykey" json:"id"`
	TaskID     uint      `gorm:"index;not null" json:"task_id"`
	Target     string    `gorm:"size:255;not null" json:"target"`   // 扫描目标
	InfoType   string    `gorm:"size:50;not null" json:"info_type"` // 信息类型：fingerprint, directory, service, etc.
	Category   string    `gorm:"size:50" json:"category"`           // 分类：server, framework, cms, etc.
	Name       string    `gorm:"size:100" json:"name"`              // 名称：如Apache, PHP, WordPress等
	Value      string    `gorm:"size:255" json:"value"`             // 值：如版本号、路径等
	Confidence float64   `gorm:"default:0" json:"confidence"`       // 置信度
	Evidence   string    `gorm:"type:text" json:"evidence"`         // 证据信息
	StatusCode int       `gorm:"default:0" json:"status_code"`      // HTTP状态码（目录发现时使用）
	Metadata   string    `gorm:"type:text" json:"metadata"`         // 额外元数据（JSON格式）
	CreatedAt  time.Time `json:"created_at"`

	// 关联关系
	Task ScanTask `gorm:"foreignKey:TaskID" json:"task,omitempty"`
}

// TableName 指定表名
func (ScanLog) TableName() string {
	return "scan_logs"
}

// TableName 指定表名
func (InfoGatheringLog) TableName() string {
	return "info_gathering_logs"
}
