// 信息收集相关类型定义

/**
 * 信息收集日志
 */
export interface InfoGatheringLog {
  id: number
  task_id: number
  target: string
  info_type: string // fingerprint, directory, service, technology
  category: string
  name: string
  value: string
  confidence: number
  evidence: string
  status_code: number
  metadata: string
  created_at: string
}

/**
 * 信息收集统计
 */
export interface InfoGatheringStats {
  fingerprint: number
  directory: number
  service: number
  technology: number
  total: number
}

/**
 * 信息收集日志响应
 */
export interface InfoGatheringLogsResponse {
  data: {
    logs: InfoGatheringLog[]
    total: number
    page: number
    size: number
  }
}

/**
 * 指纹识别结果
 */
export interface FingerprintResult {
  technology: string
  category: string
  version: string
  confidence: number
  evidence: string[]
  metadata: Record<string, string>
}

/**
 * 目录扫描结果
 */
export interface DirectoryResult {
  path: string
  status_code: number
  size: number
  title: string
  server: string
  evidence: string
}

/**
 * 服务识别结果
 */
export interface ServiceResult {
  service_name: string
  version: string
  port: number
  protocol: string
  evidence: string
}

/**
 * 技术栈识别结果
 */
export interface TechnologyResult {
  tech_type: string
  name: string
  version: string
  confidence: number
  evidence: string
  metadata: Record<string, any>
}

/**
 * 信息收集报告
 */
export interface InfoGatheringReport {
  fingerprint_summary: Record<string, string[]>
  directory_summary: Record<string, string[]>
  statistics: {
    total_fingerprints: number
    total_directories: number
    scan_timestamp: string
  }
}

/**
 * 信息收集引擎统计
 */
export interface InfoGatheringEngineStats {
  total_scans: number
  fingerprints_found: number
  directories_found: number
  services_found: number
  technologies_found: number
  last_scan_time: string
  average_scan_duration: string
}
