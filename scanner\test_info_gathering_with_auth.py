#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
带认证的信息收集功能测试脚本
"""

import requests
import json
import time

# 配置
BASE_URL = "http://localhost:8082/api/v1"
TEST_TARGET = "https://dvwa.bachang.org"

# 认证信息
USERNAME = "admin"
PASSWORD = "admin123"

def login():
    """登录获取token"""
    print("🔐 正在登录...")
    
    login_data = {
        "username": USERNAME,
        "password": PASSWORD
    }
    
    try:
        response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
        if response.status_code == 200:
            data = response.json()
            token = data.get('data', {}).get('token')
            if token:
                print("✅ 登录成功")
                return token
            else:
                print("❌ 登录响应中没有token")
                return None
        else:
            print(f"❌ 登录失败: {response.text}")
            return None
    except Exception as e:
        print(f"❌ 登录请求失败: {e}")
        return None

def get_headers(token):
    """获取带认证的请求头"""
    return {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }

def test_info_gathering_apis(token):
    """测试信息收集相关的API接口"""
    
    print("\n🔍 开始测试信息收集API接口...")
    headers = get_headers(token)
    
    # 测试任务ID（假设存在一个任务）
    task_id = 1
    
    # 1. 测试获取信息收集统计
    print(f"\n📈 测试获取信息收集统计 (任务ID: {task_id})")
    try:
        response = requests.get(f"{BASE_URL}/info-gathering/stats/{task_id}", headers=headers)
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"统计信息: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"错误响应: {response.text}")
    except Exception as e:
        print(f"请求失败: {e}")

def create_test_scan_task(token):
    """创建一个测试扫描任务"""
    
    print(f"\n🚀 创建测试扫描任务: {TEST_TARGET}")
    headers = get_headers(token)
    
    # 扫描任务配置
    task_data = {
        "name": "信息收集测试任务",
        "type": "web",
        "description": "测试信息收集功能的扫描任务",
        "targets": [TEST_TARGET],
        "schedule_type": "immediate",
        "timeout": 300,
        "threads": 1,
        "config": {
            "scan_engines": ["enhanced_web_scanner"],
            "max_concurrent": 1
        }
    }
    
    try:
        response = requests.post(f"{BASE_URL}/scans", json=task_data, headers=headers)
        print(f"状态码: {response.status_code}")
        if response.status_code == 200 or response.status_code == 201:
            data = response.json()
            task_id = data.get('data', {}).get('id')
            print(f"✅ 扫描任务创建成功，任务ID: {task_id}")
            return task_id
        else:
            print(f"❌ 创建任务失败: {response.text}")
            return None
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return None

def monitor_scan_progress(task_id, token):
    """监控扫描进度"""
    
    print(f"\n⏳ 监控扫描进度 (任务ID: {task_id})")
    headers = get_headers(token)
    
    max_wait_time = 120  # 最大等待2分钟
    start_time = time.time()
    
    while time.time() - start_time < max_wait_time:
        try:
            response = requests.get(f"{BASE_URL}/scans/{task_id}", headers=headers)
            if response.status_code == 200:
                data = response.json()
                task_data = data.get('data', {})
                status = task_data.get('status', 'unknown')
                progress = task_data.get('progress', 0)
                
                print(f"📊 扫描状态: {status}, 进度: {progress}%")
                
                if status in ['completed', 'failed', 'cancelled']:
                    print(f"✅ 扫描已完成，最终状态: {status}")
                    return status
                
                time.sleep(3)  # 等待3秒后再次检查
            else:
                print(f"❌ 获取任务状态失败: {response.text}")
                break
        except Exception as e:
            print(f"❌ 监控失败: {e}")
            break
    
    print("⏰ 监控超时")
    return None

def test_info_gathering_after_scan(task_id, token):
    """扫描完成后测试信息收集结果"""
    
    print(f"\n🔍 测试扫描完成后的信息收集结果 (任务ID: {task_id})")
    headers = get_headers(token)
    
    # 等待一下确保数据已保存
    time.sleep(2)
    
    # 测试各种信息收集API
    endpoints = [
        ("指纹识别", f"/info-gathering/fingerprints/{task_id}"),
        ("目录发现", f"/info-gathering/directories/{task_id}"),
        ("服务识别", f"/info-gathering/services/{task_id}"),
        ("技术栈", f"/info-gathering/technologies/{task_id}"),
        ("统计信息", f"/info-gathering/stats/{task_id}")
    ]
    
    for name, endpoint in endpoints:
        try:
            response = requests.get(f"{BASE_URL}{endpoint}", headers=headers)
            print(f"\n📋 {name}:")
            print(f"  状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                if 'data' in data:
                    if isinstance(data['data'], list):
                        print(f"  数据数量: {len(data['data'])}")
                        if data['data']:
                            print(f"  示例数据: {json.dumps(data['data'][0], indent=4, ensure_ascii=False)}")
                    else:
                        print(f"  数据内容: {json.dumps(data['data'], indent=4, ensure_ascii=False)}")
                else:
                    print(f"  响应数据: {json.dumps(data, indent=4, ensure_ascii=False)}")
            else:
                print(f"  错误: {response.text}")
        except Exception as e:
            print(f"  请求失败: {e}")

def test_scan_logs(task_id, token):
    """测试扫描日志"""
    
    print(f"\n📝 测试扫描日志 (任务ID: {task_id})")
    headers = get_headers(token)
    
    try:
        response = requests.get(f"{BASE_URL}/scan-logs/{task_id}", headers=headers)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            logs = data.get('data', [])
            print(f"日志数量: {len(logs)}")
            
            # 显示最近的几条日志
            for i, log in enumerate(logs[:5]):
                print(f"  日志 {i+1}: [{log.get('level')}] {log.get('stage')} - {log.get('message')}")
        else:
            print(f"错误: {response.text}")
    except Exception as e:
        print(f"请求失败: {e}")

def main():
    """主函数"""
    
    print("🎯 漏洞扫描器信息收集功能测试（带认证）")
    print("=" * 60)
    
    # 1. 登录获取token
    token = login()
    if not token:
        print("❌ 无法获取认证token，退出测试")
        return
    
    # 2. 测试API接口是否可用
    test_info_gathering_apis(token)
    
    # 3. 创建测试扫描任务
    task_id = create_test_scan_task(token)
    if not task_id:
        print("❌ 无法创建测试任务，退出测试")
        return
    
    # 4. 监控扫描进度
    final_status = monitor_scan_progress(task_id, token)
    
    # 5. 测试扫描日志
    test_scan_logs(task_id, token)
    
    # 6. 测试信息收集结果（无论扫描是否完成）
    test_info_gathering_after_scan(task_id, token)
    
    print("\n✅ 信息收集功能测试完成！")

if __name__ == "__main__":
    main()
