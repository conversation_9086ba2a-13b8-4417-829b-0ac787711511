package handlers

import (
	"net/http"
	"strconv"

	"scanner/internal/services"
	"scanner/pkg/response"

	"github.com/gin-gonic/gin"
)

// InfoGatheringHandler 信息收集处理器
type InfoGatheringHandler struct {
	infoGatheringService *services.InfoGatheringService
}

// NewInfoGatheringHandler 创建信息收集处理器
func NewInfoGatheringHandler(infoGatheringService *services.InfoGatheringService) *InfoGatheringHandler {
	return &InfoGatheringHandler{
		infoGatheringService: infoGatheringService,
	}
}

// GetInfoGatheringLogs 获取信息收集日志
// @Summary 获取信息收集日志
// @Description 获取指定任务的信息收集日志，包括指纹识别、目录发现等信息
// @Tags 信息收集
// @Accept json
// @Produce json
// @Param task_id path int true "任务ID"
// @Param info_type query string false "信息类型" Enums(fingerprint,directory,service,technology)
// @Param page query int false "页码" default(1)
// @Param size query int false "每页数量" default(20)
// @Success 200 {object} response.Response{data=object}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/info-gathering/logs/{task_id} [get]
func (h *InfoGatheringHandler) GetInfoGatheringLogs(c *gin.Context) {
	// 获取任务ID
	taskIDStr := c.Param("task_id")
	taskID, err := strconv.ParseUint(taskIDStr, 10, 32)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "无效的任务ID", "INVALID_TASK_ID", nil)
		return
	}

	// 获取查询参数
	infoType := c.Query("info_type")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	size, _ := strconv.Atoi(c.DefaultQuery("size", "20"))

	if page < 1 {
		page = 1
	}
	if size < 1 || size > 100 {
		size = 20
	}

	// 获取信息收集日志
	logs, total, err := h.infoGatheringService.GetInfoGatheringLogs(uint(taskID), infoType, page, size)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "获取信息收集日志失败: "+err.Error(), "DATABASE_ERROR", nil)
		return
	}

	// 构建响应数据
	data := gin.H{
		"logs":  logs,
		"total": total,
		"page":  page,
		"size":  size,
	}

	response.SuccessWithMessage(c, "获取信息收集日志成功", data)
}

// GetFingerprintsByTask 获取任务的指纹识别信息
// @Summary 获取指纹识别信息
// @Description 获取指定任务的指纹识别信息
// @Tags 信息收集
// @Accept json
// @Produce json
// @Param task_id path int true "任务ID"
// @Success 200 {object} response.Response{data=[]models.InfoGatheringLog}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/info-gathering/fingerprints/{task_id} [get]
func (h *InfoGatheringHandler) GetFingerprintsByTask(c *gin.Context) {
	// 获取任务ID
	taskIDStr := c.Param("task_id")
	taskID, err := strconv.ParseUint(taskIDStr, 10, 32)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "无效的任务ID", "INVALID_TASK_ID", nil)
		return
	}

	// 获取指纹识别信息
	fingerprints, err := h.infoGatheringService.GetFingerprintsByTask(uint(taskID))
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "获取指纹识别信息失败: "+err.Error(), "DATABASE_ERROR", nil)
		return
	}

	response.SuccessWithMessage(c, "获取指纹识别信息成功", fingerprints)
}

// GetDirectoriesByTask 获取任务的目录发现信息
// @Summary 获取目录发现信息
// @Description 获取指定任务的目录发现信息
// @Tags 信息收集
// @Accept json
// @Produce json
// @Param task_id path int true "任务ID"
// @Success 200 {object} response.Response{data=[]models.InfoGatheringLog}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/info-gathering/directories/{task_id} [get]
func (h *InfoGatheringHandler) GetDirectoriesByTask(c *gin.Context) {
	// 获取任务ID
	taskIDStr := c.Param("task_id")
	taskID, err := strconv.ParseUint(taskIDStr, 10, 32)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "无效的任务ID")
		return
	}

	// 获取目录发现信息
	directories, err := h.infoGatheringService.GetDirectoriesByTask(uint(taskID))
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "获取目录发现信息失败: "+err.Error())
		return
	}

	response.Success(c, "获取目录发现信息成功", directories)
}

// GetServicesByTask 获取任务的服务识别信息
// @Summary 获取服务识别信息
// @Description 获取指定任务的服务识别信息
// @Tags 信息收集
// @Accept json
// @Produce json
// @Param task_id path int true "任务ID"
// @Success 200 {object} response.Response{data=[]models.InfoGatheringLog}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/info-gathering/services/{task_id} [get]
func (h *InfoGatheringHandler) GetServicesByTask(c *gin.Context) {
	// 获取任务ID
	taskIDStr := c.Param("task_id")
	taskID, err := strconv.ParseUint(taskIDStr, 10, 32)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "无效的任务ID")
		return
	}

	// 获取服务识别信息
	services, err := h.infoGatheringService.GetServicesByTask(uint(taskID))
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "获取服务识别信息失败: "+err.Error())
		return
	}

	response.Success(c, "获取服务识别信息成功", services)
}

// GetTechnologiesByTask 获取任务的技术栈信息
// @Summary 获取技术栈信息
// @Description 获取指定任务的技术栈信息
// @Tags 信息收集
// @Accept json
// @Produce json
// @Param task_id path int true "任务ID"
// @Success 200 {object} response.Response{data=[]models.InfoGatheringLog}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/info-gathering/technologies/{task_id} [get]
func (h *InfoGatheringHandler) GetTechnologiesByTask(c *gin.Context) {
	// 获取任务ID
	taskIDStr := c.Param("task_id")
	taskID, err := strconv.ParseUint(taskIDStr, 10, 32)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "无效的任务ID")
		return
	}

	// 获取技术栈信息
	technologies, err := h.infoGatheringService.GetTechnologiesByTask(uint(taskID))
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "获取技术栈信息失败: "+err.Error())
		return
	}

	response.Success(c, "获取技术栈信息成功", technologies)
}

// GetInfoGatheringStats 获取信息收集统计
// @Summary 获取信息收集统计
// @Description 获取指定任务的信息收集统计信息
// @Tags 信息收集
// @Accept json
// @Produce json
// @Param task_id path int true "任务ID"
// @Success 200 {object} response.Response{data=object}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/info-gathering/stats/{task_id} [get]
func (h *InfoGatheringHandler) GetInfoGatheringStats(c *gin.Context) {
	// 获取任务ID
	taskIDStr := c.Param("task_id")
	taskID, err := strconv.ParseUint(taskIDStr, 10, 32)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "无效的任务ID")
		return
	}

	// 获取信息收集统计
	stats, err := h.infoGatheringService.GetInfoGatheringStats(uint(taskID))
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "获取信息收集统计失败: "+err.Error())
		return
	}

	response.Success(c, "获取信息收集统计成功", stats)
}

// DeleteInfoGatheringLogs 删除信息收集日志
// @Summary 删除信息收集日志
// @Description 删除指定任务的信息收集日志
// @Tags 信息收集
// @Accept json
// @Produce json
// @Param task_id path int true "任务ID"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/info-gathering/logs/{task_id} [delete]
func (h *InfoGatheringHandler) DeleteInfoGatheringLogs(c *gin.Context) {
	// 获取任务ID
	taskIDStr := c.Param("task_id")
	taskID, err := strconv.ParseUint(taskIDStr, 10, 32)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "无效的任务ID")
		return
	}

	// 删除信息收集日志
	if err := h.infoGatheringService.DeleteInfoGatheringLogs(uint(taskID)); err != nil {
		response.Error(c, http.StatusInternalServerError, "删除信息收集日志失败: "+err.Error())
		return
	}

	response.Success(c, "删除信息收集日志成功", nil)
}
