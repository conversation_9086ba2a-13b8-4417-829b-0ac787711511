package rules

import (
	"context"
	"fmt"
	"net/http"
	"path/filepath"
	"sync"
	"time"

	"scanner/internal/scanner/types"
	"scanner/internal/services"
	"scanner/pkg/logger"
)

// RuleManager 规则管理器
// 负责加载、管理和执行各种检测规则
type RuleManager struct {
	// 规则引擎
	ruleEngine *RuleEngine
	cveEngine  *CVERuleEngine

	// 规则缓存
	webRules     []*DetectionRule
	networkRules []*DetectionRule
	cveRules     []*CVERule

	// 配置
	rulesDir  string
	cveDBPath string
	enabled   bool

	// 同步
	mutex sync.RWMutex

	// 服务依赖
	vulnService *services.VulnerabilityService
	logService  *services.ScanLogService

	// 统计
	stats *RuleManagerStats
}

// RuleManagerStats 规则管理器统计
type RuleManagerStats struct {
	TotalRules       int64     `json:"total_rules"`
	WebRules         int64     `json:"web_rules"`
	NetworkRules     int64     `json:"network_rules"`
	CVERules         int64     `json:"cve_rules"`
	MatchedRules     int64     `json:"matched_rules"`
	LastUpdate       time.Time `json:"last_update"`
	LastRuleMatch    time.Time `json:"last_rule_match"`
	MatchSuccessRate float64   `json:"match_success_rate"`
}

// RuleMatchResult 规则匹配结果
type RuleMatchResult struct {
	RuleID     string                 `json:"rule_id"`
	RuleName   string                 `json:"rule_name"`
	RuleType   string                 `json:"rule_type"`
	Severity   string                 `json:"severity"`
	Confidence float64                `json:"confidence"`
	Matched    bool                   `json:"matched"`
	Evidence   string                 `json:"evidence"`
	Metadata   map[string]interface{} `json:"metadata"`
	MatchTime  time.Time              `json:"match_time"`
	Target     string                 `json:"target"`
	CVEInfo    *CVEInfo               `json:"cve_info,omitempty"`
}

// CVEInfo CVE信息
type CVEInfo struct {
	CVEID       string  `json:"cve_id"`
	CVSSScore   float64 `json:"cvss_score"`
	Description string  `json:"description"`
	References  string  `json:"references"`
}

// NewRuleManager 创建规则管理器
func NewRuleManager(rulesDir, cveDBPath string, vulnService *services.VulnerabilityService, logService *services.ScanLogService) *RuleManager {
	manager := &RuleManager{
		rulesDir:    rulesDir,
		cveDBPath:   cveDBPath,
		enabled:     true,
		vulnService: vulnService,
		logService:  logService,
		stats: &RuleManagerStats{
			LastUpdate: time.Now(),
		},
	}

	// 初始化规则引擎
	manager.ruleEngine = NewRuleEngine()
	manager.cveEngine = NewCVERuleEngine(cveDBPath)

	// 加载规则
	if err := manager.LoadRules(); err != nil {
		logger.Errorf("加载规则失败: %v", err)
	}

	return manager
}

// LoadRules 加载所有规则
func (rm *RuleManager) LoadRules() error {
	rm.mutex.Lock()
	defer rm.mutex.Unlock()

	logger.Info("开始加载检测规则...")

	// 加载Web规则
	webRulesPath := filepath.Join(rm.rulesDir, "web_*.json")
	if err := rm.ruleEngine.LoadRulesFromDirectory(rm.rulesDir); err != nil {
		logger.Errorf("加载Web规则失败: %v", err)
	}

	// 加载网络规则 - 暂时使用相同的加载方法
	// networkRulesPath := filepath.Join(rm.rulesDir, "network_*.json")
	// if err := rm.ruleEngine.LoadRulesFromPattern(networkRulesPath); err != nil {
	// 	logger.Errorf("加载网络规则失败: %v", err)
	// }

	// 加载CVE规则 - 暂时简化实现
	// cveRulesPath := filepath.Join(rm.rulesDir, "cve")
	// if err := rm.cveEngine.LoadCVERulesFromDirectory(cveRulesPath); err != nil {
	// 	logger.Errorf("加载CVE规则失败: %v", err)
	// }

	// 更新统计
	rm.updateStats()

	logger.Infof("规则加载完成 - Web规则: %d, 网络规则: %d, CVE规则: %d",
		rm.stats.WebRules, rm.stats.NetworkRules, rm.stats.CVERules)

	return nil
}

// MatchWebRules 匹配Web规则
func (rm *RuleManager) MatchWebRules(ctx context.Context, target string, request *http.Request, response *http.Response) ([]*RuleMatchResult, error) {
	if !rm.enabled {
		return nil, nil
	}

	rm.mutex.RLock()
	defer rm.mutex.RUnlock()

	var results []*RuleMatchResult

	// 使用规则引擎进行匹配
	matches := rm.ruleEngine.MatchRules(target, request, response)

	for _, match := range matches {
		result := &RuleMatchResult{
			RuleID:     match.Rule.ID,
			RuleName:   match.Rule.Name,
			RuleType:   match.Rule.Type,
			Severity:   match.Rule.Severity,
			Confidence: match.Confidence,
			Matched:    true,
			Evidence:   match.Evidence,
			Metadata:   match.Metadata,
			MatchTime:  time.Now(),
			Target:     target,
		}

		results = append(results, result)

		// 记录匹配日志
		if rm.logService != nil {
			rm.logService.LogInfo(0, "规则匹配", target,
				fmt.Sprintf("匹配到规则: %s (%s)", match.Rule.Name, match.Rule.Severity), 0)
		}
	}

	// 更新统计
	rm.stats.MatchedRules += int64(len(results))
	rm.stats.LastRuleMatch = time.Now()

	return results, nil
}

// MatchCVERules 匹配CVE规则
func (rm *RuleManager) MatchCVERules(ctx context.Context, target string, fingerprints []string, services []string) ([]*RuleMatchResult, error) {
	if !rm.enabled {
		return nil, nil
	}

	rm.mutex.RLock()
	defer rm.mutex.RUnlock()

	var results []*RuleMatchResult

	// 基于指纹和服务信息匹配CVE
	cveMatches := rm.cveEngine.MatchCVEsByFingerprints(fingerprints, services)

	for _, match := range cveMatches {
		result := &RuleMatchResult{
			RuleID:     match.CVEID,
			RuleName:   match.Name,
			RuleType:   "cve",
			Severity:   match.Severity,
			Confidence: match.Confidence,
			Matched:    true,
			Evidence:   match.Evidence,
			MatchTime:  time.Now(),
			Target:     target,
			CVEInfo: &CVEInfo{
				CVEID:       match.CVEID,
				CVSSScore:   match.CVSSScore,
				Description: match.Description,
				References:  match.References,
			},
		}

		results = append(results, result)

		// 记录CVE匹配日志
		if rm.logService != nil {
			rm.logService.LogInfo(0, "CVE匹配", target,
				fmt.Sprintf("匹配到CVE: %s (CVSS: %.1f)", match.CVEID, match.CVSSScore), 0)
		}
	}

	return results, nil
}

// CreateVulnerabilityFromRule 从规则匹配结果创建漏洞
func (rm *RuleManager) CreateVulnerabilityFromRule(taskID uint, result *RuleMatchResult) error {
	if rm.vulnService == nil {
		return fmt.Errorf("漏洞服务未初始化")
	}

	// 构建漏洞信息
	vuln := &types.Vulnerability{
		TaskID:      taskID,
		Target:      result.Target,
		Name:        result.RuleName,
		Description: fmt.Sprintf("规则匹配检测到的漏洞: %s", result.RuleName),
		Severity:    result.Severity,
		Confidence:  result.Confidence,
		Evidence:    result.Evidence,
		Solution:    "请根据漏洞类型采取相应的修复措施",
		References:  "",
		Tags:        []string{result.RuleType, "rule-based"},
		Metadata: map[string]interface{}{
			"rule_id":    result.RuleID,
			"rule_type":  result.RuleType,
			"match_time": result.MatchTime,
		},
		Status:    "open",
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// 如果是CVE规则，添加CVE信息
	if result.CVEInfo != nil {
		vuln.CVEID = result.CVEInfo.CVEID
		vuln.CVSSScore = result.CVEInfo.CVSSScore
		vuln.References = result.CVEInfo.References
		vuln.Description = result.CVEInfo.Description
		vuln.Tags = append(vuln.Tags, "cve")
	}

	// 保存漏洞
	return rm.vulnService.CreateVulnerability(vuln)
}

// updateStats 更新统计信息
func (rm *RuleManager) updateStats() {
	rm.stats.WebRules = int64(len(rm.ruleEngine.GetRulesByType("web")))
	rm.stats.NetworkRules = int64(len(rm.ruleEngine.GetRulesByType("network")))
	rm.stats.CVERules = int64(rm.cveEngine.GetTotalCVECount())
	rm.stats.TotalRules = rm.stats.WebRules + rm.stats.NetworkRules + rm.stats.CVERules
	rm.stats.LastUpdate = time.Now()

	// 计算匹配成功率
	if rm.stats.TotalRules > 0 {
		rm.stats.MatchSuccessRate = float64(rm.stats.MatchedRules) / float64(rm.stats.TotalRules) * 100
	}
}

// GetStats 获取统计信息
func (rm *RuleManager) GetStats() *RuleManagerStats {
	rm.mutex.RLock()
	defer rm.mutex.RUnlock()

	// 更新统计
	rm.updateStats()

	return rm.stats
}

// IsEnabled 检查是否启用
func (rm *RuleManager) IsEnabled() bool {
	rm.mutex.RLock()
	defer rm.mutex.RUnlock()
	return rm.enabled
}

// SetEnabled 设置启用状态
func (rm *RuleManager) SetEnabled(enabled bool) {
	rm.mutex.Lock()
	defer rm.mutex.Unlock()
	rm.enabled = enabled

	logger.Infof("规则管理器状态更新: %v", enabled)
}

// ReloadRules 重新加载规则
func (rm *RuleManager) ReloadRules() error {
	logger.Info("重新加载检测规则...")
	return rm.LoadRules()
}
