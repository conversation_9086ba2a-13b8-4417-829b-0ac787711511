package scanner

import (
	"log"
	"scanner/internal/scanner/engines"
	"scanner/internal/services"
	"scanner/internal/services/vulnerability"
	"time"
)

// InitializeEngines 初始化所有扫描引擎
func InitializeEngines(logService *services.ScanLogService, systemConfigService *services.SystemConfigService, vulnerabilityService *vulnerability.Service) (*EngineManager, *TaskScheduler, error) {
	// 创建引擎管理器
	engineManager := NewEngineManager(logService, systemConfigService, vulnerabilityService)

	// 注册增强Web扫描引擎（包含爬虫、信息收集、漏洞检测等完整功能）
	enhancedWebConfig := &engines.EnhancedWebConfig{
		CrawlerEnabled:     true, // 启用爬虫功能
		AdvancedDetection:  true,
		SmartPayloads:      true,
		MaxDepth:           3,
		MaxPages:           100,
		RequestTimeout:     30 * time.Second,
		ConcurrentRequests: 10,
	}
	webEngine := engines.NewEnhancedWebEngine(enhancedWebConfig)
	if err := engineManager.RegisterEngine(webEngine); err != nil {
		return nil, nil, err
	}
	log.Printf("已注册增强Web扫描引擎: %s (包含爬虫、信息收集、漏洞检测、验证等完整功能)", webEngine.GetName())

	// 注册网络扫描引擎
	networkEngine := engines.NewNetworkEngine()
	if err := engineManager.RegisterEngine(networkEngine); err != nil {
		return nil, nil, err
	}
	log.Printf("已注册网络扫描引擎: %s", networkEngine.GetName())

	// 注册域名发现引擎
	domainEngine := engines.NewDomainEngine()
	if err := engineManager.RegisterEngine(domainEngine); err != nil {
		return nil, nil, err
	}
	log.Printf("已注册域名发现引擎: %s", domainEngine.GetName())

	// 创建任务调度器
	scheduler := NewTaskScheduler(engineManager)
	log.Println("任务调度器已初始化")

	log.Printf("扫描引擎系统初始化完成，共注册 %d 个引擎", len(engineManager.GetEngines()))

	return engineManager, scheduler, nil
}

// ShutdownEngines 关闭扫描引擎系统
func ShutdownEngines(engineManager *EngineManager, scheduler *TaskScheduler) error {
	log.Println("正在关闭扫描引擎系统...")

	// 关闭任务调度器
	if scheduler != nil {
		if err := scheduler.Shutdown(); err != nil {
			log.Printf("关闭任务调度器失败: %v", err)
		}
	}

	// 关闭引擎管理器
	if engineManager != nil {
		if err := engineManager.Shutdown(); err != nil {
			log.Printf("关闭引擎管理器失败: %v", err)
		}
	}

	log.Println("扫描引擎系统已关闭")
	return nil
}
