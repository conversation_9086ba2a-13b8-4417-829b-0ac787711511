#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的规则引擎测试
"""

import requests
import json
import time

def test_simple_scan():
    """测试简单扫描"""
    print("🎯 简单规则引擎测试")
    print("=" * 50)
    
    # 登录获取token
    login_data = {"username": "admin", "password": "admin123"}
    response = requests.post("http://localhost:8082/api/v1/auth/login", json=login_data, timeout=5)
    
    if response.status_code != 200:
        print("❌ 登录失败")
        return
    
    token = response.json().get('data', {}).get('token')
    headers = {"Authorization": f"Bearer {token}"}
    
    print("✅ 登录成功")
    
    # 创建简单的Web扫描任务
    scan_data = {
        "name": f"规则测试-{int(time.time())}",
        "type": "web",
        "targets": ["http://httpbin.org/html"],
        "config": {
            "scan_type": "web",
            "timeout": 30
        }
    }
    
    # 创建扫描任务
    response = requests.post("http://localhost:8082/api/v1/scans", 
                           json=scan_data, headers=headers, timeout=10)
    
    if response.status_code != 200:
        print(f"❌ 创建扫描任务失败: {response.status_code}")
        print(f"响应内容: {response.text}")
        return
    
    task_data = response.json().get('data', {})
    task_id = task_data.get('id')
    print(f"✅ 创建扫描任务成功，任务ID: {task_id}")
    
    # 启动扫描
    start_response = requests.post(f"http://localhost:8082/api/v1/scans/{task_id}/start",
                                 headers=headers, timeout=10)
    
    if start_response.status_code == 200:
        print("✅ 扫描任务已启动")
        
        # 等待扫描完成
        print("\n📊 等待扫描完成...")
        time.sleep(30)  # 等待30秒
        
        # 检查扫描日志
        print(f"\n📋 检查扫描日志:")
        try:
            response = requests.get(f"http://localhost:8082/api/v1/scans/{task_id}/logs",
                                  headers=headers, timeout=5)
            
            if response.status_code == 200:
                logs_data = response.json().get('data', [])
                
                print(f"  总日志数: {len(logs_data)}")
                
                # 查找规则相关日志
                rule_logs = [log for log in logs_data if '规则' in log.get('message', '')]
                http_logs = [log for log in logs_data if 'HTTP' in log.get('message', '') or '请求' in log.get('message', '')]
                
                print(f"  规则相关日志: {len(rule_logs)} 条")
                print(f"  HTTP请求日志: {len(http_logs)} 条")
                
                # 显示最新的几条日志
                print("  📝 最新日志:")
                for i, log in enumerate(logs_data[-10:], 1):
                    print(f"    {i}. [{log.get('level', 'INFO')}] {log.get('stage', 'unknown')}: {log.get('message', 'unknown')}")
                
        except Exception as e:
            print(f"❌ 检查扫描日志失败: {e}")
        
    else:
        print(f"❌ 启动扫描失败: {start_response.status_code}")
        print(f"响应内容: {start_response.text}")
    
    print("\n" + "=" * 50)
    print("📋 测试完成")

if __name__ == "__main__":
    test_simple_scan()
