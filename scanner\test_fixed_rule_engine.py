#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的规则引擎功能
"""

import requests
import json
import time

def test_rule_engine_integration():
    """测试规则引擎集成"""
    print("🎯 测试修复后的规则引擎功能")
    print("=" * 60)
    
    # 登录获取token
    login_data = {"username": "admin", "password": "admin123"}
    response = requests.post("http://localhost:8082/api/v1/auth/login", json=login_data, timeout=5)
    
    if response.status_code != 200:
        print("❌ 登录失败")
        return
    
    token = response.json().get('data', {}).get('token')
    headers = {"Authorization": f"Bearer {token}"}
    
    print("✅ 登录成功，获取到token")
    
    # 创建测试扫描任务
    scan_data = {
        "name": "规则引擎修复测试",
        "type": "web",
        "targets": ["http://testphp.vulnweb.com/artists.php?artist=1"],
        "config": {
            "scan_type": "web",
            "enable_crawler": False,
            "max_depth": 1,
            "timeout": 30
        }
    }
    
    # 创建扫描任务
    response = requests.post("http://localhost:8082/api/v1/scans", 
                           json=scan_data, headers=headers, timeout=10)
    
    if response.status_code != 200:
        print(f"❌ 创建扫描任务失败: {response.status_code}")
        print(f"响应内容: {response.text}")
        return
    
    task_data = response.json().get('data', {})
    task_id = task_data.get('id')
    print(f"✅ 创建扫描任务成功，任务ID: {task_id}")
    
    # 启动扫描
    start_response = requests.post(f"http://localhost:8082/api/v1/scans/{task_id}/start",
                                 headers=headers, timeout=10)
    
    if start_response.status_code != 200:
        print(f"❌ 启动扫描失败: {start_response.status_code}")
        print(f"响应内容: {start_response.text}")
        return
    
    print("✅ 扫描任务已启动")
    
    # 监控扫描进度
    print("\n📊 监控扫描进度:")
    max_wait_time = 120  # 最大等待2分钟
    start_time = time.time()
    
    while time.time() - start_time < max_wait_time:
        try:
            response = requests.get(f"http://localhost:8082/api/v1/scans/{task_id}",
                                  headers=headers, timeout=5)
            
            if response.status_code == 200:
                task_data = response.json().get('data', {})
                status = task_data.get('status', 'unknown')
                progress = task_data.get('progress', 0)
                
                print(f"  状态: {status}, 进度: {progress}%")
                
                if status in ['completed', 'failed', 'cancelled']:
                    print(f"✅ 扫描完成，最终状态: {status}")
                    break
                    
            time.sleep(5)  # 等待5秒后再次检查
            
        except Exception as e:
            print(f"❌ 获取扫描状态失败: {e}")
            break
    
    # 检查扫描日志中的规则匹配信息
    print(f"\n📋 检查扫描日志:")
    try:
        response = requests.get(f"http://localhost:8082/api/v1/scans/{task_id}/logs",
                              headers=headers, timeout=5)
        
        if response.status_code == 200:
            logs_data = response.json().get('data', [])
            
            rule_logs = []
            http_logs = []
            for log in logs_data:
                message = log.get('message', '').lower()
                if '规则' in message or 'rule' in message:
                    rule_logs.append(log)
                if 'http' in message or '请求' in message:
                    http_logs.append(log)
            
            print(f"  规则相关日志: {len(rule_logs)} 条")
            print(f"  HTTP请求日志: {len(http_logs)} 条")
            
            if rule_logs:
                print("  📝 规则日志详情:")
                for i, log in enumerate(rule_logs[:5], 1):
                    print(f"    {i}. [{log.get('level', 'INFO')}] {log.get('message', 'unknown')}")
            
            if http_logs:
                print("  📝 HTTP请求日志详情:")
                for i, log in enumerate(http_logs[:3], 1):
                    print(f"    {i}. [{log.get('level', 'INFO')}] {log.get('message', 'unknown')}")
                    
        else:
            print(f"❌ 获取扫描日志失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 检查扫描日志失败: {e}")
    
    # 检查发现的漏洞
    print(f"\n🔍 检查发现的漏洞:")
    try:
        vuln_response = requests.get(f"http://localhost:8082/api/v1/vulnerabilities?task_id={task_id}",
                                   headers=headers, timeout=5)
        
        if vuln_response.status_code == 200:
            vuln_data = vuln_response.json().get('data', {})
            vulnerabilities = vuln_data.get('vulnerabilities', [])
            
            print(f"  总漏洞数: {len(vulnerabilities)}")
            
            rule_based_vulns = []
            for vuln in vulnerabilities:
                tags = vuln.get('tags', [])
                if isinstance(tags, list) and 'rule-based' in tags:
                    rule_based_vulns.append(vuln)
                elif isinstance(tags, str) and 'rule-based' in tags:
                    rule_based_vulns.append(vuln)
            
            print(f"  规则匹配漏洞: {len(rule_based_vulns)}")
            
            if rule_based_vulns:
                print(f"  📝 规则匹配漏洞详情:")
                for i, vuln in enumerate(rule_based_vulns[:3], 1):
                    print(f"    {i}. {vuln.get('name', 'unknown')}")
                    print(f"       严重程度: {vuln.get('severity', 'unknown')}")
                    print(f"       目标: {vuln.get('url', 'unknown')}")
                    print(f"       证据: {vuln.get('evidence', 'unknown')[:100]}...")
            else:
                print("  ⚠️ 未发现基于规则的漏洞")
                
        else:
            print(f"❌ 获取漏洞列表失败: {vuln_response.status_code}")
            
    except Exception as e:
        print(f"❌ 检查漏洞失败: {e}")
    
    print("\n" + "=" * 60)
    print("📋 测试总结:")
    print("1. 扫描任务创建: ✅ 成功")
    print("2. 扫描任务启动: ✅ 成功")
    print("3. HTTP请求功能: 🔍 已修复")
    print("4. 规则匹配功能: 🎯 等待验证")
    print("5. 漏洞检测功能: 📊 等待验证")

if __name__ == "__main__":
    test_rule_engine_integration()
