#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新扫描任务的技术栈信息显示
"""

import requests
import json
import time

def test_new_scan_task():
    """创建新扫描任务并测试技术栈信息显示"""
    print("🎯 测试新扫描任务的技术栈信息显示")
    print("=" * 60)
    
    # 登录获取token
    login_data = {"username": "admin", "password": "admin123"}
    response = requests.post("http://localhost:8082/api/v1/auth/login", json=login_data, timeout=5)
    
    if response.status_code != 200:
        print("❌ 登录失败")
        return
    
    token = response.json().get('data', {}).get('token')
    headers = {"Authorization": f"Bearer {token}"}
    
    print("✅ 登录成功")
    
    # 创建一个新的Web扫描任务
    scan_data = {
        "name": f"新技术栈测试-{int(time.time())}",
        "type": "web",
        "targets": ["http://testphp.vulnweb.com"],
        "config": {
            "scan_type": "web",
            "timeout": 60
        }
    }
    
    # 创建扫描任务
    response = requests.post("http://localhost:8082/api/v1/scans", 
                           json=scan_data, headers=headers, timeout=10)
    
    if response.status_code != 200:
        print(f"❌ 创建扫描任务失败: {response.status_code}")
        print(f"响应内容: {response.text}")
        return
    
    task_data = response.json().get('data', {})
    task_id = task_data.get('id')
    print(f"✅ 创建扫描任务成功，任务ID: {task_id}")
    
    # 等待扫描完成
    print("\n📊 等待扫描完成...")
    max_wait_time = 120  # 最大等待2分钟
    start_time = time.time()
    
    while time.time() - start_time < max_wait_time:
        try:
            response = requests.get(f"http://localhost:8082/api/v1/scans/{task_id}",
                                  headers=headers, timeout=5)
            
            if response.status_code == 200:
                task_data = response.json().get('data', {})
                status = task_data.get('status', 'unknown')
                progress = task_data.get('progress', 0)
                
                print(f"  状态: {status}, 进度: {progress}%")
                
                if status in ['completed', 'failed', 'cancelled']:
                    print(f"✅ 扫描完成，最终状态: {status}")
                    break
                    
            time.sleep(10)  # 等待10秒后再次检查
            
        except Exception as e:
            print(f"❌ 获取扫描状态失败: {e}")
            break
    
    # 检查目标信息
    print(f"\n🔍 检查新任务的目标信息:")
    try:
        response = requests.get(f"http://localhost:8082/api/v1/scans/{task_id}/target-info",
                              headers=headers, timeout=5)
        
        if response.status_code == 200:
            target_info = response.json().get('data', {})
            targets = target_info.get('targets', [])
            
            if targets:
                print(f"📋 找到 {len(targets)} 个目标")
                
                for i, target in enumerate(targets, 1):
                    print(f"\n🎯 目标 {i}:")
                    
                    # 检查技术栈信息
                    tech_stack = target.get('tech_stack', {})
                    print(f"  🔧 技术栈信息:")
                    print(f"    Web服务器: {tech_stack.get('web_server', '未检测到')}")
                    print(f"    编程语言: {tech_stack.get('language', '未检测到')}")
                    print(f"    开发框架: {tech_stack.get('framework', '未检测到')}")
                    print(f"    数据库: {tech_stack.get('database', '未检测到')}")
                    print(f"    CMS系统: {tech_stack.get('cms', '未检测到')}")
                    
                    # 检查新的technologies字段
                    technologies = tech_stack.get('technologies', [])
                    libraries = tech_stack.get('libraries', [])
                    print(f"    技术组件(technologies): {technologies}")
                    print(f"    库组件(libraries): {libraries}")
                    
                    # 验证字段映射
                    if tech_stack.get('language'):
                        print(f"    ✅ 编程语言字段正确: {tech_stack.get('language')}")
                    else:
                        print(f"    ❌ 编程语言字段为空")
                        
                    if 'technologies' in tech_stack:
                        print(f"    ✅ technologies字段存在")
                    else:
                        print(f"    ❌ technologies字段不存在")
                        
                    # 打印完整的tech_stack数据
                    print(f"    📊 完整tech_stack数据: {json.dumps(tech_stack, ensure_ascii=False, indent=6)}")
            else:
                print(f"  ❌ 没有找到目标数据")
                
        else:
            print(f"❌ 获取目标信息失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 检查目标信息失败: {e}")
    
    print("\n" + "=" * 60)
    print("📋 新任务测试总结:")
    print("1. 扫描任务创建: ✅ 成功")
    print("2. 扫描任务完成: 🔍 已测试")
    print("3. 技术栈信息: 🎯 已验证")
    print("4. 字段映射: 📊 已检查")
    print(f"5. 任务ID: {task_id}")

if __name__ == "__main__":
    test_new_scan_task()
