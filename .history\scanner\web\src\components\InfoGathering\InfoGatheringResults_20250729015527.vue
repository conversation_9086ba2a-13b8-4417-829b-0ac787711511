<template>
  <div class="info-gathering-results">
    <!-- 统计概览 -->
    <div class="stats-overview">
      <el-row :gutter="16">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon fingerprint">
                <el-icon><Search /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.fingerprint || 0 }}</div>
                <div class="stat-label">指纹识别</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon directory">
                <el-icon><Folder /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.directory || 0 }}</div>
                <div class="stat-label">目录发现</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon service">
                <el-icon><Service /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.service || 0 }}</div>
                <div class="stat-label">服务识别</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon technology">
                <el-icon><Monitor /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.technology || 0 }}</div>
                <div class="stat-label">技术栈</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 详细信息标签页 -->
    <el-card class="details-card">
      <el-tabs v-model="activeTab" @tab-click="handleTabClick">
        <!-- 指纹识别 -->
        <el-tab-pane label="指纹识别" name="fingerprints">
          <div class="fingerprint-list">
            <el-table :data="fingerprints" stripe>
              <el-table-column prop="target" label="目标" width="200" />
              <el-table-column prop="category" label="分类" width="120">
                <template #default="{ row }">
                  <el-tag :type="getCategoryTagType(row.category)">
                    {{ getCategoryDisplayName(row.category) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="name" label="技术名称" width="150" />
              <el-table-column prop="value" label="版本" width="120" />
              <el-table-column prop="confidence" label="置信度" width="100">
                <template #default="{ row }">
                  <el-progress 
                    :percentage="Math.round(row.confidence * 100)" 
                    :color="getConfidenceColor(row.confidence)"
                    :stroke-width="8"
                  />
                </template>
              </el-table-column>
              <el-table-column prop="evidence" label="证据" min-width="200" show-overflow-tooltip />
              <el-table-column prop="created_at" label="发现时间" width="160">
                <template #default="{ row }">
                  {{ formatTime(row.created_at) }}
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>

        <!-- 目录发现 -->
        <el-tab-pane label="目录发现" name="directories">
          <div class="directory-list">
            <el-table :data="directories" stripe>
              <el-table-column prop="target" label="目标" width="200" />
              <el-table-column prop="value" label="路径" width="200" />
              <el-table-column prop="status_code" label="状态码" width="100">
                <template #default="{ row }">
                  <el-tag :type="getStatusCodeTagType(row.status_code)">
                    {{ row.status_code }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="evidence" label="详细信息" min-width="300" show-overflow-tooltip />
              <el-table-column prop="created_at" label="发现时间" width="160">
                <template #default="{ row }">
                  {{ formatTime(row.created_at) }}
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>

        <!-- 服务识别 -->
        <el-tab-pane label="服务识别" name="services">
          <div class="service-list">
            <el-table :data="services" stripe>
              <el-table-column prop="target" label="目标" width="200" />
              <el-table-column prop="name" label="服务名称" width="150" />
              <el-table-column prop="value" label="版本" width="120" />
              <el-table-column prop="metadata" label="端口/协议" width="150">
                <template #default="{ row }">
                  <span v-if="getMetadata(row.metadata)">
                    {{ getMetadata(row.metadata).port }}:{{ getMetadata(row.metadata).protocol }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column prop="evidence" label="证据" min-width="200" show-overflow-tooltip />
              <el-table-column prop="created_at" label="发现时间" width="160">
                <template #default="{ row }">
                  {{ formatTime(row.created_at) }}
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>

        <!-- 技术栈 -->
        <el-tab-pane label="技术栈" name="technologies">
          <div class="technology-list">
            <el-table :data="technologies" stripe>
              <el-table-column prop="target" label="目标" width="200" />
              <el-table-column prop="category" label="技术类型" width="120">
                <template #default="{ row }">
                  <el-tag :type="getCategoryTagType(row.category)">
                    {{ getCategoryDisplayName(row.category) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="name" label="技术名称" width="150" />
              <el-table-column prop="value" label="版本" width="120" />
              <el-table-column prop="confidence" label="置信度" width="100">
                <template #default="{ row }">
                  <el-progress 
                    :percentage="Math.round(row.confidence * 100)" 
                    :color="getConfidenceColor(row.confidence)"
                    :stroke-width="8"
                  />
                </template>
              </el-table-column>
              <el-table-column prop="evidence" label="证据" min-width="200" show-overflow-tooltip />
              <el-table-column prop="created_at" label="发现时间" width="160">
                <template #default="{ row }">
                  {{ formatTime(row.created_at) }}
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Folder, Tools, Monitor } from '@element-plus/icons-vue'
import { infoGatheringApi } from '@/api/infoGathering'
import type { InfoGatheringLog, InfoGatheringStats } from '@/types/infoGathering'

// Props
interface Props {
  taskId: number
}

const props = defineProps<Props>()

// 响应式数据
const activeTab = ref('fingerprints')
const loading = ref(false)

// 统计数据
const stats = reactive<InfoGatheringStats>({
  fingerprint: 0,
  directory: 0,
  service: 0,
  technology: 0,
  total: 0
})

// 详细数据
const fingerprints = ref<InfoGatheringLog[]>([])
const directories = ref<InfoGatheringLog[]>([])
const services = ref<InfoGatheringLog[]>([])
const technologies = ref<InfoGatheringLog[]>([])

// 监听任务ID变化
watch(() => props.taskId, (newTaskId) => {
  if (newTaskId) {
    loadData()
  }
}, { immediate: true })

// 加载数据
const loadData = async () => {
  if (!props.taskId) return

  loading.value = true
  try {
    // 加载统计数据
    await loadStats()
    
    // 根据当前标签页加载对应数据
    await loadTabData(activeTab.value)
  } catch (error) {
    console.error('加载信息收集数据失败:', error)
    ElMessage.error('加载信息收集数据失败')
  } finally {
    loading.value = false
  }
}

// 加载统计数据
const loadStats = async () => {
  try {
    const response = await infoGatheringApi.getStats(props.taskId)
    Object.assign(stats, response.data)
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

// 加载标签页数据
const loadTabData = async (tabName: string) => {
  try {
    switch (tabName) {
      case 'fingerprints':
        if (fingerprints.value.length === 0) {
          const response = await infoGatheringApi.getFingerprints(props.taskId)
          fingerprints.value = response.data
        }
        break
      case 'directories':
        if (directories.value.length === 0) {
          const response = await infoGatheringApi.getDirectories(props.taskId)
          directories.value = response.data
        }
        break
      case 'services':
        if (services.value.length === 0) {
          const response = await infoGatheringApi.getServices(props.taskId)
          services.value = response.data
        }
        break
      case 'technologies':
        if (technologies.value.length === 0) {
          const response = await infoGatheringApi.getTechnologies(props.taskId)
          technologies.value = response.data
        }
        break
    }
  } catch (error) {
    console.error(`加载${tabName}数据失败:`, error)
  }
}

// 标签页切换处理
const handleTabClick = (tab: any) => {
  loadTabData(tab.name)
}

// 获取分类显示名称
const getCategoryDisplayName = (category: string) => {
  const displayNames: Record<string, string> = {
    server: '服务器',
    framework: '框架',
    cms: 'CMS',
    database: '数据库',
    language: '编程语言',
    library: '库',
    os: '操作系统',
    cdn: 'CDN',
    waf: '防火墙',
    path_discovery: '路径发现',
    service_discovery: '服务发现'
  }
  return displayNames[category] || category
}

// 获取分类标签类型
const getCategoryTagType = (category: string) => {
  const tagTypes: Record<string, string> = {
    server: 'primary',
    framework: 'success',
    cms: 'warning',
    database: 'danger',
    language: 'info',
    library: '',
    os: 'primary',
    cdn: 'success',
    waf: 'danger'
  }
  return tagTypes[category] || ''
}

// 获取状态码标签类型
const getStatusCodeTagType = (statusCode: number) => {
  if (statusCode >= 200 && statusCode < 300) return 'success'
  if (statusCode >= 300 && statusCode < 400) return 'warning'
  if (statusCode >= 400) return 'danger'
  return ''
}

// 获取置信度颜色
const getConfidenceColor = (confidence: number) => {
  if (confidence >= 0.8) return '#67c23a'
  if (confidence >= 0.6) return '#e6a23c'
  return '#f56c6c'
}

// 解析元数据
const getMetadata = (metadata: string) => {
  try {
    return JSON.parse(metadata || '{}')
  } catch {
    return {}
  }
}

// 格式化时间
const formatTime = (time: string) => {
  return new Date(time).toLocaleString('zh-CN')
}

// 组件挂载时加载数据
onMounted(() => {
  if (props.taskId) {
    loadData()
  }
})
</script>

<style scoped>
.info-gathering-results {
  padding: 16px;
}

.stats-overview {
  margin-bottom: 20px;
}

.stat-card {
  border-radius: 8px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 8px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.stat-icon.fingerprint {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.directory {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.service {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.technology {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 4px;
}

.details-card {
  border-radius: 8px;
}

.fingerprint-list,
.directory-list,
.service-list,
.technology-list {
  min-height: 300px;
}

:deep(.el-table) {
  border-radius: 8px;
}

:deep(.el-table th) {
  background-color: #f8f9fa;
  color: #606266;
  font-weight: 600;
}

:deep(.el-progress-bar__outer) {
  border-radius: 4px;
}

:deep(.el-tag) {
  border-radius: 4px;
}
</style>
