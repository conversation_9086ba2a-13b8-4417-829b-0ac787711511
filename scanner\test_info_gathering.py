#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
信息收集功能测试脚本
用于测试扫描过程中的信息收集功能是否正常工作
"""

import requests
import json
import time

# 配置
BASE_URL = "http://localhost:8082/api/v1"
TEST_TARGET = "https://dvwa.bachang.org"

def test_info_gathering_apis():
    """测试信息收集相关的API接口"""
    
    print("🔍 开始测试信息收集API接口...")
    
    # 测试任务ID（假设存在一个任务）
    task_id = 1
    
    # 1. 测试获取信息收集日志
    print(f"\n📊 测试获取信息收集日志 (任务ID: {task_id})")
    try:
        response = requests.get(f"{BASE_URL}/info-gathering/logs/{task_id}")
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"错误响应: {response.text}")
    except Exception as e:
        print(f"请求失败: {e}")
    
    # 2. 测试获取指纹识别信息
    print(f"\n🔍 测试获取指纹识别信息 (任务ID: {task_id})")
    try:
        response = requests.get(f"{BASE_URL}/info-gathering/fingerprints/{task_id}")
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"指纹识别数量: {len(data.get('data', []))}")
            if data.get('data'):
                print(f"示例指纹: {json.dumps(data['data'][0], indent=2, ensure_ascii=False)}")
        else:
            print(f"错误响应: {response.text}")
    except Exception as e:
        print(f"请求失败: {e}")
    
    # 3. 测试获取目录发现信息
    print(f"\n📁 测试获取目录发现信息 (任务ID: {task_id})")
    try:
        response = requests.get(f"{BASE_URL}/info-gathering/directories/{task_id}")
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"目录发现数量: {len(data.get('data', []))}")
            if data.get('data'):
                print(f"示例目录: {json.dumps(data['data'][0], indent=2, ensure_ascii=False)}")
        else:
            print(f"错误响应: {response.text}")
    except Exception as e:
        print(f"请求失败: {e}")
    
    # 4. 测试获取统计信息
    print(f"\n📈 测试获取信息收集统计 (任务ID: {task_id})")
    try:
        response = requests.get(f"{BASE_URL}/info-gathering/stats/{task_id}")
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"统计信息: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"错误响应: {response.text}")
    except Exception as e:
        print(f"请求失败: {e}")

def create_test_scan_task():
    """创建一个测试扫描任务"""
    
    print(f"\n🚀 创建测试扫描任务: {TEST_TARGET}")
    
    # 扫描任务配置
    task_data = {
        "name": "信息收集测试任务",
        "description": "测试信息收集功能的扫描任务",
        "scan_type": "web",
        "targets": [TEST_TARGET],
        "config": {
            "scan_engines": ["enhanced_web_scanner"],
            "max_concurrent": 1,
            "timeout": 300
        }
    }
    
    try:
        response = requests.post(f"{BASE_URL}/scans", json=task_data)
        print(f"状态码: {response.status_code}")
        if response.status_code == 200 or response.status_code == 201:
            data = response.json()
            task_id = data.get('data', {}).get('id')
            print(f"✅ 扫描任务创建成功，任务ID: {task_id}")
            return task_id
        else:
            print(f"❌ 创建任务失败: {response.text}")
            return None
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return None

def monitor_scan_progress(task_id):
    """监控扫描进度"""
    
    print(f"\n⏳ 监控扫描进度 (任务ID: {task_id})")
    
    max_wait_time = 300  # 最大等待5分钟
    start_time = time.time()
    
    while time.time() - start_time < max_wait_time:
        try:
            response = requests.get(f"{BASE_URL}/scans/{task_id}")
            if response.status_code == 200:
                data = response.json()
                task_data = data.get('data', {})
                status = task_data.get('status', 'unknown')
                progress = task_data.get('progress', 0)
                
                print(f"📊 扫描状态: {status}, 进度: {progress}%")
                
                if status in ['completed', 'failed', 'cancelled']:
                    print(f"✅ 扫描已完成，最终状态: {status}")
                    return status
                
                time.sleep(5)  # 等待5秒后再次检查
            else:
                print(f"❌ 获取任务状态失败: {response.text}")
                break
        except Exception as e:
            print(f"❌ 监控失败: {e}")
            break
    
    print("⏰ 监控超时")
    return None

def test_info_gathering_after_scan(task_id):
    """扫描完成后测试信息收集结果"""
    
    print(f"\n🔍 测试扫描完成后的信息收集结果 (任务ID: {task_id})")
    
    # 等待一下确保数据已保存
    time.sleep(2)
    
    # 测试各种信息收集API
    endpoints = [
        ("指纹识别", f"/info-gathering/fingerprints/{task_id}"),
        ("目录发现", f"/info-gathering/directories/{task_id}"),
        ("服务识别", f"/info-gathering/services/{task_id}"),
        ("技术栈", f"/info-gathering/technologies/{task_id}"),
        ("统计信息", f"/info-gathering/stats/{task_id}")
    ]
    
    for name, endpoint in endpoints:
        try:
            response = requests.get(f"{BASE_URL}{endpoint}")
            print(f"\n📋 {name}:")
            print(f"  状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                if 'data' in data:
                    if isinstance(data['data'], list):
                        print(f"  数据数量: {len(data['data'])}")
                        if data['data']:
                            print(f"  示例数据: {json.dumps(data['data'][0], indent=4, ensure_ascii=False)}")
                    else:
                        print(f"  数据内容: {json.dumps(data['data'], indent=4, ensure_ascii=False)}")
                else:
                    print(f"  响应数据: {json.dumps(data, indent=4, ensure_ascii=False)}")
            else:
                print(f"  错误: {response.text}")
        except Exception as e:
            print(f"  请求失败: {e}")

def main():
    """主函数"""
    
    print("🎯 漏洞扫描器信息收集功能测试")
    print("=" * 50)
    
    # 1. 首先测试API接口是否可用
    test_info_gathering_apis()
    
    # 2. 创建测试扫描任务
    task_id = create_test_scan_task()
    if not task_id:
        print("❌ 无法创建测试任务，退出测试")
        return
    
    # 3. 监控扫描进度
    final_status = monitor_scan_progress(task_id)
    if final_status != 'completed':
        print(f"❌ 扫描未正常完成，状态: {final_status}")
        return
    
    # 4. 测试信息收集结果
    test_info_gathering_after_scan(task_id)
    
    print("\n✅ 信息收集功能测试完成！")

if __name__ == "__main__":
    main()
