package engines

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"scanner/internal/scanner/types"
	"scanner/internal/services"
	"scanner/pkg/logger"
)

// IntegratedInfoGatheringEngine 集成信息收集引擎
// 整合指纹识别、目录发现等功能，并将结果保存到数据库
type IntegratedInfoGatheringEngine struct {
	name                 string
	fingerprintEngine    *EnhancedFingerprintEngine
	directoryScanner     *EnhancedDirectoryScanner
	infoGatheringService *services.InfoGatheringService
	scanLogService       *services.ScanLogService
	stats                *InfoGatheringStats
}

// InfoGatheringStats 信息收集统计
type InfoGatheringStats struct {
	TotalScans          int64         `json:"total_scans"`
	FingerprintsFound   int64         `json:"fingerprints_found"`
	DirectoriesFound    int64         `json:"directories_found"`
	ServicesFound       int64         `json:"services_found"`
	TechnologiesFound   int64         `json:"technologies_found"`
	LastScanTime        time.Time     `json:"last_scan_time"`
	AverageScanDuration time.Duration `json:"average_scan_duration"`
}

// NewIntegratedInfoGatheringEngine 创建集成信息收集引擎
func NewIntegratedInfoGatheringEngine(infoGatheringService *services.InfoGatheringService, scanLogService *services.ScanLogService) *IntegratedInfoGatheringEngine {
	fingerprintEngine := NewEnhancedFingerprintEngine(infoGatheringService, scanLogService)
	directoryScanner := NewEnhancedDirectoryScanner(infoGatheringService, scanLogService)

	return &IntegratedInfoGatheringEngine{
		name:                 "integrated_info_gathering",
		fingerprintEngine:    fingerprintEngine,
		directoryScanner:     directoryScanner,
		infoGatheringService: infoGatheringService,
		scanLogService:       scanLogService,
		stats:                &InfoGatheringStats{},
	}
}

// GetName 获取引擎名称
func (e *IntegratedInfoGatheringEngine) GetName() string {
	return e.name
}

// GetType 获取引擎类型
func (e *IntegratedInfoGatheringEngine) GetType() string {
	return "info_gathering"
}

// Scan 执行完整的信息收集扫描
func (e *IntegratedInfoGatheringEngine) Scan(ctx context.Context, target *types.ScanTarget, config *types.ScanConfig, progress chan<- *types.ScanProgress) (*types.ScanResult, error) {
	startTime := time.Now()
	logger.Infof("🔍 开始集成信息收集: %s", target.Value)

	// 更新统计
	e.stats.TotalScans++
	e.stats.LastScanTime = startTime

	// 创建扫描结果
	result := &types.ScanResult{
		TaskID:          fmt.Sprintf("info-gathering-%d", time.Now().Unix()),
		TargetID:        target.Value,
		Status:          "running",
		Metadata:        make(map[string]interface{}),
		StartTime:       startTime,
		Vulnerabilities: make([]*types.Vulnerability, 0),
		Errors:          make([]string, 0),
	}

	// 从配置中获取任务ID
	var taskID uint
	if config != nil && config.TaskID != "" {
		// 将字符串任务ID转换为uint
		if id, err := strconv.ParseUint(config.TaskID, 10, 32); err == nil {
			taskID = uint(id)
		} else {
			logger.Warnf("无法解析任务ID: %s, 使用默认值", config.TaskID)
			taskID = 1
		}
	}

	// 第一阶段：指纹识别 (0-50%)
	if progress != nil {
		progress <- &types.ScanProgress{
			TaskID:      result.TaskID,
			Progress:    10,
			CurrentItem: "指纹识别",
			Message:     "开始指纹识别",
			Timestamp:   time.Now(),
		}
	}

	logger.Info("🔍 第一阶段：指纹识别")
	if e.scanLogService != nil {
		e.scanLogService.LogInfo(taskID, "信息收集", target.Value, "开始指纹识别", 10)
	}

	fingerprintResults, err := e.fingerprintEngine.IdentifyTechnologies(ctx, taskID, target.Value)
	if err != nil {
		logger.Errorf("指纹识别失败: %v", err)
		result.Errors = append(result.Errors, fmt.Sprintf("指纹识别失败: %v", err))
	} else {
		e.stats.FingerprintsFound += int64(len(fingerprintResults))
		result.Metadata["fingerprints"] = fingerprintResults
		logger.Infof("指纹识别完成，发现 %d 个技术", len(fingerprintResults))
	}

	if progress != nil {
		progress <- &types.ScanProgress{
			TaskID:      result.TaskID,
			Progress:    50,
			CurrentItem: "指纹识别",
			Message:     fmt.Sprintf("指纹识别完成，发现 %d 个技术", len(fingerprintResults)),
			Timestamp:   time.Now(),
		}
	}

	// 第二阶段：目录扫描 (50-90%)
	if progress != nil {
		progress <- &types.ScanProgress{
			TaskID:      result.TaskID,
			Progress:    60,
			CurrentItem: "目录扫描",
			Message:     "开始目录扫描",
			Timestamp:   time.Now(),
		}
	}

	logger.Info("📁 第二阶段：目录扫描")
	if e.scanLogService != nil {
		e.scanLogService.LogInfo(taskID, "信息收集", target.Value, "开始目录扫描", 60)
	}

	directoryResults, err := e.directoryScanner.ScanDirectories(ctx, taskID, target.Value)
	if err != nil {
		logger.Errorf("目录扫描失败: %v", err)
		result.Errors = append(result.Errors, fmt.Sprintf("目录扫描失败: %v", err))
	} else {
		e.stats.DirectoriesFound += int64(len(directoryResults))
		result.Metadata["directories"] = directoryResults
		logger.Infof("目录扫描完成，发现 %d 个可访问目录", len(directoryResults))
	}

	if progress != nil {
		progress <- &types.ScanProgress{
			TaskID:      result.TaskID,
			Progress:    90,
			CurrentItem: "目录扫描",
			Message:     fmt.Sprintf("目录扫描完成，发现 %d 个可访问目录", len(directoryResults)),
			Timestamp:   time.Now(),
		}
	}

	// 第三阶段：结果汇总 (90-100%)
	if progress != nil {
		progress <- &types.ScanProgress{
			TaskID:      result.TaskID,
			Progress:    95,
			CurrentItem: "结果汇总",
			Message:     "汇总信息收集结果",
			Timestamp:   time.Now(),
		}
	}

	logger.Info("📊 第三阶段：结果汇总")
	if e.scanLogService != nil {
		e.scanLogService.LogInfo(taskID, "信息收集", target.Value, "汇总信息收集结果", 95)
	}

	// 生成信息收集报告
	report := e.generateInfoGatheringReport(fingerprintResults, directoryResults)
	result.Metadata["info_gathering_report"] = report

	// 完成扫描
	result.Status = "completed"
	result.EndTime = time.Now()
	duration := result.EndTime.Sub(result.StartTime)
	e.stats.AverageScanDuration = duration

	// 更新结果元数据
	result.Metadata["scan_duration"] = duration.String()
	result.Metadata["fingerprints_count"] = len(fingerprintResults)
	result.Metadata["directories_count"] = len(directoryResults)
	result.Metadata["engine_type"] = "integrated_info_gathering"

	if progress != nil {
		progress <- &types.ScanProgress{
			TaskID:      result.TaskID,
			Progress:    100,
			CurrentItem: "完成",
			Message:     "信息收集完成",
			Timestamp:   time.Now(),
		}
	}

	logger.Infof("✅ 集成信息收集完成: %s (耗时: %v)", target.Value, duration)
	if e.scanLogService != nil {
		e.scanLogService.LogInfo(taskID, "信息收集", target.Value,
			fmt.Sprintf("信息收集完成 (耗时: %v)", duration), 100)
	}

	return result, nil
}

// generateInfoGatheringReport 生成信息收集报告
func (e *IntegratedInfoGatheringEngine) generateInfoGatheringReport(fingerprints []*FingerprintResult, directories []*DirectoryResult) map[string]interface{} {
	report := make(map[string]interface{})

	// 指纹识别汇总
	fingerprintSummary := make(map[string][]string)
	for _, fp := range fingerprints {
		if _, exists := fingerprintSummary[fp.Category]; !exists {
			fingerprintSummary[fp.Category] = make([]string, 0)
		}
		tech := fp.Technology
		if fp.Version != "" {
			tech += " " + fp.Version
		}
		fingerprintSummary[fp.Category] = append(fingerprintSummary[fp.Category], tech)
	}
	report["fingerprint_summary"] = fingerprintSummary

	// 目录发现汇总
	directorySummary := make(map[string][]string)
	for _, dir := range directories {
		statusGroup := fmt.Sprintf("%dxx", dir.StatusCode/100)
		if _, exists := directorySummary[statusGroup]; !exists {
			directorySummary[statusGroup] = make([]string, 0)
		}
		directorySummary[statusGroup] = append(directorySummary[statusGroup], dir.Path)
	}
	report["directory_summary"] = directorySummary

	// 统计信息
	report["statistics"] = map[string]interface{}{
		"total_fingerprints": len(fingerprints),
		"total_directories":  len(directories),
		"scan_timestamp":     time.Now(),
	}

	return report
}

// IsHealthy 检查引擎健康状态
func (e *IntegratedInfoGatheringEngine) IsHealthy() bool {
	return true
}

// GetStats 获取统计信息
func (e *IntegratedInfoGatheringEngine) GetStats() interface{} {
	return e.stats
}

// GetSupportedTargets 获取支持的目标类型
func (e *IntegratedInfoGatheringEngine) GetSupportedTargets() []string {
	return []string{"url", "domain", "ip"}
}

// IsEnabled 检查引擎是否启用
func (e *IntegratedInfoGatheringEngine) IsEnabled() bool {
	return true
}

// SetLogService 设置日志服务
func (e *IntegratedInfoGatheringEngine) SetLogService(logService *services.ScanLogService) {
	e.scanLogService = logService
}

// Validate 验证配置
func (e *IntegratedInfoGatheringEngine) Validate(config *types.ScanConfig) error {
	if config == nil {
		return fmt.Errorf("扫描配置不能为空")
	}
	return nil
}

// Stop 停止引擎
func (e *IntegratedInfoGatheringEngine) Stop(ctx context.Context, taskID string) error {
	logger.Infof("集成信息收集引擎已停止，任务ID: %s", taskID)
	return nil
}
