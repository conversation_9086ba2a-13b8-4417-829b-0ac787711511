package main

import (
	"fmt"
	"log"
	"strings"

	"scanner/internal/models"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

func main() {
	fmt.Println("🔍 检查任务327的信息收集数据")
	fmt.Println(strings.Repeat("=", 50))

	// 连接数据库
	db, err := gorm.Open(sqlite.Open("data/scanner.db"), &gorm.Config{})
	if err != nil {
		log.Fatal("连接数据库失败:", err)
	}

	// 查询任务327
	var task models.ScanTask
	if err := db.Where("id = ?", 327).First(&task).Error; err != nil {
		log.Fatal("查询任务327失败:", err)
	}

	fmt.Printf("📋 任务327详细信息:\n")
	fmt.Printf("  ID: %d\n", task.ID)
	fmt.Printf("  名称: %s\n", task.Name)
	fmt.Printf("  类型: %s\n", task.Type)
	fmt.Printf("  状态: %s\n", task.Status)
	fmt.Printf("  进度: %d%%\n", task.Progress)
	fmt.Printf("  目标: %s\n", task.Targets)
	fmt.Printf("  创建时间: %s\n", task.CreatedAt.Format("2006-01-02 15:04:05"))
	fmt.Printf("  开始时间: %s\n", task.StartTime.Format("2006-01-02 15:04:05"))
	fmt.Printf("  结束时间: %s\n", task.EndTime.Format("2006-01-02 15:04:05"))

	fmt.Printf("\n🔧 信息收集数据:\n")
	if task.InfoGatheringData != "" {
		fmt.Printf("  数据长度: %d 字符\n", len(task.InfoGatheringData))
		fmt.Printf("  数据内容: %s\n", task.InfoGatheringData)
	} else {
		fmt.Printf("  ❌ 信息收集数据为空\n")
	}

	fmt.Printf("\n📊 扫描统计:\n")
	fmt.Printf("  总目标数: %d\n", task.TotalTargets)
	fmt.Printf("  已扫描目标数: %d\n", task.ScannedTargets)
	fmt.Printf("  总请求数: %d\n", task.TotalRequests)
	fmt.Printf("  成功请求数: %d\n", task.SuccessRequests)
	fmt.Printf("  失败请求数: %d\n", task.FailedRequests)
	fmt.Printf("  漏洞数量: %d\n", task.VulnCount)

	// 检查扫描日志
	fmt.Printf("\n📋 扫描日志 (最新10条):\n")
	var logs []models.ScanLog
	if err := db.Where("task_id = ?", 327).Order("created_at DESC").Limit(10).Find(&logs).Error; err != nil {
		fmt.Printf("  ❌ 查询扫描日志失败: %v\n", err)
	} else {
		for i, log := range logs {
			fmt.Printf("  %d. [%s] %s: %s\n", i+1, log.Level, log.Stage, log.Message)
		}
	}

	// 检查是否有指纹识别相关的日志
	fmt.Printf("\n🔍 指纹识别相关日志:\n")
	var fingerprintLogs []models.ScanLog
	if err := db.Where("task_id = ? AND (message LIKE '%指纹识别%' OR message LIKE '%PHP%' OR message LIKE '%Nginx%')", 327).Order("created_at ASC").Find(&fingerprintLogs).Error; err != nil {
		fmt.Printf("  ❌ 查询指纹识别日志失败: %v\n", err)
	} else {
		for i, log := range fingerprintLogs {
			fmt.Printf("  %d. [%s] %s: %s\n", i+1, log.Level, log.Stage, log.Message)
		}
	}

	fmt.Printf("\n" + "="*50)
	fmt.Printf("\n📋 总结:\n")
	if task.InfoGatheringData != "" {
		fmt.Printf("✅ 信息收集数据已保存\n")
	} else {
		fmt.Printf("❌ 信息收集数据未保存\n")
	}

	if len(fingerprintLogs) > 0 {
		fmt.Printf("✅ 指纹识别正常执行\n")
	} else {
		fmt.Printf("❌ 指纹识别未执行\n")
	}
}
