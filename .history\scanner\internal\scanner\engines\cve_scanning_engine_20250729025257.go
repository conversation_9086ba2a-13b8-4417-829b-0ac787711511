package engines

import (
	"context"
	"fmt"
	"sort"
	"strings"
	"sync"
	"time"

	"scanner/internal/scanner/cve"
	"scanner/internal/scanner/fingerprint"
	"scanner/internal/scanner/types"
	"scanner/internal/services"
	"scanner/internal/services/vulnerability"
	"scanner/pkg/logger"
)

// CVEScanningEngine CVE扫描引擎
// 实现历史漏洞覆盖、自动化利用检测、漏洞库集成等功能
// 支持从2000年开始的完整CVE历史漏洞扫描
type CVEScanningEngine struct {
	// 基础组件
	name    string
	enabled bool
	mutex   sync.RWMutex

	// CVE核心组件
	cveDatabase       *cve.CVEDatabase
	cveManager        *cve.CVEManager
	fingerprintEngine *fingerprint.FingerprintEngine

	// 扫描组件
	vulnerabilityMatcher *VulnerabilityMatcher
	pocExecutor          *CVEPoCExecutor
	exploitDetector      *ExploitDetector

	// 历史扫描组件
	historicalScanner *HistoricalCVEScanner
	yearlyAnalyzer    *YearlyVulnerabilityAnalyzer
	trendAnalyzer     *VulnerabilityTrendAnalyzer

	// 配置和统计
	config *CVEScanningConfig
	stats  *CVEScanningStats

	// 服务依赖
	logService  *services.ScanLogService
	vulnService *vulnerability.Service
}

// CVEScanningConfig CVE扫描配置
type CVEScanningConfig struct {
	// 基础配置
	EnableHistoricalScan bool `json:"enable_historical_scan"` // 启用历史扫描
	StartYear            int  `json:"start_year"`             // 开始年份
	EndYear              int  `json:"end_year"`               // 结束年份
	MaxCVEsPerTarget     int  `json:"max_cves_per_target"`    // 每个目标最大CVE数

	// 扫描策略
	ScanStrategy        string   `json:"scan_strategy"`        // 扫描策略: comprehensive, targeted, fast
	ConfidenceThreshold float64  `json:"confidence_threshold"` // 置信度阈值
	SeverityFilter      []string `json:"severity_filter"`      // 严重程度过滤

	// PoC验证配置
	EnablePoCVerification bool          `json:"enable_poc_verification"` // 启用PoC验证
	PoCTimeout            time.Duration `json:"poc_timeout"`             // PoC超时时间
	MaxPoCAttempts        int           `json:"max_poc_attempts"`        // 最大PoC尝试次数

	// 利用检测配置
	EnableExploitDetection bool     `json:"enable_exploit_detection"` // 启用利用检测
	ExploitSources         []string `json:"exploit_sources"`          // 利用代码源

	// 性能配置
	ConcurrentScans      int           `json:"concurrent_scans"`       // 并发扫描数
	MaxConcurrentScans   int           `json:"max_concurrent_scans"`   // 最大并发扫描数
	ScanTimeout          time.Duration `json:"scan_timeout"`           // 扫描超时时间
	RequestTimeout       time.Duration `json:"request_timeout"`        // 请求超时时间
	CacheEnabled         bool          `json:"cache_enabled"`          // 启用缓存
	CacheTTL             time.Duration `json:"cache_ttl"`              // 缓存TTL
	MaxCacheSize         int           `json:"max_cache_size"`         // 最大缓存大小
	CacheExpiration      time.Duration `json:"cache_expiration"`       // 缓存过期时间
	EnableGCOptimization bool          `json:"enable_gc_optimization"` // 启用垃圾回收优化
	CacheStrategy        string        `json:"cache_strategy"`         // 缓存策略
	CacheHitThreshold    float64       `json:"cache_hit_threshold"`    // 缓存命中率阈值

	// 网络配置
	MaxIdleConns    int           `json:"max_idle_conns"`     // 最大空闲连接数
	MaxConnsPerHost int           `json:"max_conns_per_host"` // 每个主机最大连接数
	IdleConnTimeout time.Duration `json:"idle_conn_timeout"`  // 空闲连接超时
	MaxRetries      int           `json:"max_retries"`        // 最大重试次数
	RetryDelay      time.Duration `json:"retry_delay"`        // 重试延迟

	// 数据源配置
	CVEDataSources     []string      `json:"cve_data_sources"`     // CVE数据源
	ExploitDataSources []string      `json:"exploit_data_sources"` // 利用数据源
	UpdateInterval     time.Duration `json:"update_interval"`      // 更新间隔
}

// CVEScanningStats CVE扫描统计
type CVEScanningStats struct {
	TotalScans         int64 `json:"total_scans"`
	TotalCVEsScanned   int64 `json:"total_cves_scanned"`
	TotalVulnsFound    int64 `json:"total_vulns_found"`
	TotalPoCsExecuted  int64 `json:"total_pocs_executed"`
	TotalExploitsFound int64 `json:"total_exploits_found"`

	// 年度统计
	YearlyStats map[int]*YearlyStats `json:"yearly_stats"`

	// 严重程度统计
	SeverityStats map[string]int64 `json:"severity_stats"`

	// 性能统计
	AverageResponseTime time.Duration `json:"average_response_time"`
	CacheHitRate        float64       `json:"cache_hit_rate"`

	LastUpdate time.Time `json:"last_update"`
}

// YearlyStats 年度统计
type YearlyStats struct {
	Year          int   `json:"year"`
	CVEsScanned   int64 `json:"cves_scanned"`
	VulnsFound    int64 `json:"vulns_found"`
	CriticalVulns int64 `json:"critical_vulns"`
	HighVulns     int64 `json:"high_vulns"`
	MediumVulns   int64 `json:"medium_vulns"`
	LowVulns      int64 `json:"low_vulns"`
}

// VulnerabilityMatcher 漏洞匹配器
type VulnerabilityMatcher struct {
	matchingRules     []*MatchingRule
	versionComparator *VersionComparator
	productMatcher    *ProductMatcher
	mutex             sync.RWMutex
}

// MatchingRule 匹配规则
type MatchingRule struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Condition   string                 `json:"condition"`
	Weight      float64                `json:"weight"`
	Enabled     bool                   `json:"enabled"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// CVEPoCExecutor CVE PoC执行器
type CVEPoCExecutor struct {
	pocDatabase     map[string]*CVEPoCEntry
	executionEngine *PoCExecutionEngine
	resultValidator *PoCResultValidator
	safetyChecker   *PoCSecurityChecker
	mutex           sync.RWMutex
}

// CVEPoCEntry CVE专用PoC条目（与poc_manager.go中的PoCEntry区分）
type CVEPoCEntry struct {
	ID          string                 `json:"id"`
	CVEID       string                 `json:"cve_id"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Type        string                 `json:"type"` // http, script, binary
	Code        string                 `json:"code"`
	Parameters  map[string]interface{} `json:"parameters"`
	Safety      string                 `json:"safety"` // safe, moderate, dangerous
	Author      string                 `json:"author"`
	Source      string                 `json:"source"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
}

// ExploitDetector 利用检测器
type ExploitDetector struct {
	exploitDatabase map[string]*ExploitEntry
	detectionRules  []*ExploitDetectionRule
	sourceMonitor   *ExploitSourceMonitor
	mutex           sync.RWMutex
}

// ExploitEntry 利用条目
type ExploitEntry struct {
	ID          string    `json:"id"`
	CVEID       string    `json:"cve_id"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	Type        string    `json:"type"` // metasploit, exploit-db, custom
	Platform    []string  `json:"platform"`
	Reliability string    `json:"reliability"` // excellent, great, good, normal, average, low
	Source      string    `json:"source"`
	URL         string    `json:"url"`
	Code        string    `json:"code"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// ExploitDetectionRule 利用检测规则
type ExploitDetectionRule struct {
	ID         string   `json:"id"`
	Name       string   `json:"name"`
	Pattern    string   `json:"pattern"`
	Sources    []string `json:"sources"`
	Confidence float64  `json:"confidence"`
	Enabled    bool     `json:"enabled"`
}

// HistoricalCVEScanner 历史CVE扫描器
type HistoricalCVEScanner struct {
	yearRanges      []*YearRange
	scanStrategies  map[string]*HistoricalScanStrategy
	progressTracker *HistoricalScanProgress
	mutex           sync.RWMutex
}

// YearRange 年份范围
type YearRange struct {
	StartYear int `json:"start_year"`
	EndYear   int `json:"end_year"`
	Priority  int `json:"priority"`
}

// HistoricalScanStrategy 历史扫描策略
type HistoricalScanStrategy struct {
	Name        string          `json:"name"`
	Description string          `json:"description"`
	YearWeight  map[int]float64 `json:"year_weight"` // 年份权重
	Enabled     bool            `json:"enabled"`
}

// HistoricalScanProgress 历史扫描进度
type HistoricalScanProgress struct {
	TotalYears     int                   `json:"total_years"`
	CompletedYears int                   `json:"completed_years"`
	CurrentYear    int                   `json:"current_year"`
	YearProgress   map[int]*YearProgress `json:"year_progress"`
	StartTime      time.Time             `json:"start_time"`
	EstimatedEnd   time.Time             `json:"estimated_end"`
}

// YearProgress 年度进度
type YearProgress struct {
	Year        int       `json:"year"`
	TotalCVEs   int       `json:"total_cves"`
	ScannedCVEs int       `json:"scanned_cves"`
	FoundVulns  int       `json:"found_vulns"`
	Progress    float64   `json:"progress"`
	StartTime   time.Time `json:"start_time"`
	EndTime     time.Time `json:"end_time"`
}

// YearlyVulnerabilityAnalyzer 年度漏洞分析器
type YearlyVulnerabilityAnalyzer struct {
	yearlyData      map[int]*YearlyVulnerabilityData
	analysisRules   []*YearlyAnalysisRule
	trendCalculator *TrendCalculator
	mutex           sync.RWMutex
}

// YearlyVulnerabilityData 年度漏洞数据
type YearlyVulnerabilityData struct {
	Year                int                 `json:"year"`
	TotalCVEs           int                 `json:"total_cves"`
	CriticalCVEs        int                 `json:"critical_cves"`
	HighCVEs            int                 `json:"high_cves"`
	MediumCVEs          int                 `json:"medium_cves"`
	LowCVEs             int                 `json:"low_cves"`
	TopVulnerabilities  []*TopVulnerability `json:"top_vulnerabilities"`
	TechnologyBreakdown map[string]int      `json:"technology_breakdown"`
	TrendIndicators     *TrendIndicators    `json:"trend_indicators"`
}

// TopVulnerability 顶级漏洞
type TopVulnerability struct {
	CVEID       string  `json:"cve_id"`
	Score       float64 `json:"score"`
	Severity    string  `json:"severity"`
	Description string  `json:"description"`
	Impact      string  `json:"impact"`
	Prevalence  int     `json:"prevalence"`
}

// TrendIndicators 趋势指标
type TrendIndicators struct {
	GrowthRate      float64 `json:"growth_rate"`
	SeverityTrend   string  `json:"severity_trend"`   // increasing, decreasing, stable
	TechnologyTrend string  `json:"technology_trend"` // web, mobile, iot, cloud
	AttackTrend     string  `json:"attack_trend"`     // remote, local, social
}

// VulnerabilityTrendAnalyzer 漏洞趋势分析器
type VulnerabilityTrendAnalyzer struct {
	trendModels      map[string]*TrendModel
	predictionEngine *VulnerabilityPredictionEngine
	alertSystem      *TrendAlertSystem
	mutex            sync.RWMutex
}

// TrendModel 趋势模型
type TrendModel struct {
	Name        string                 `json:"name"`
	Type        string                 `json:"type"` // linear, exponential, seasonal
	Parameters  map[string]float64     `json:"parameters"`
	Accuracy    float64                `json:"accuracy"`
	LastTrained time.Time              `json:"last_trained"`
	Predictions map[string]interface{} `json:"predictions"`
}

// NewCVEScanningEngine 创建CVE扫描引擎
func NewCVEScanningEngine(logService *services.ScanLogService, vulnService *vulnerability.Service) *CVEScanningEngine {
	config := &CVEScanningConfig{
		EnableHistoricalScan:   true,
		StartYear:              2000,
		EndYear:                time.Now().Year(),
		MaxCVEsPerTarget:       1000,
		ScanStrategy:           "comprehensive",
		ConfidenceThreshold:    0.7,
		SeverityFilter:         []string{"CRITICAL", "HIGH", "MEDIUM", "LOW"},
		EnablePoCVerification:  true,
		PoCTimeout:             30 * time.Second,
		MaxPoCAttempts:         3,
		EnableExploitDetection: true,
		ExploitSources:         []string{"exploit-db", "metasploit", "github"},
		ConcurrentScans:        5,
		CacheEnabled:           true,
		CacheTTL:               24 * time.Hour,
		CVEDataSources: []string{
			"https://nvd.nist.gov/feeds/json/cve/1.1/",
			"https://cve.mitre.org/data/downloads/",
		},
		ExploitDataSources: []string{
			"https://www.exploit-db.com/",
			"https://github.com/rapid7/metasploit-framework",
		},
		UpdateInterval: 6 * time.Hour,
	}

	stats := &CVEScanningStats{
		YearlyStats:   make(map[int]*YearlyStats),
		SeverityStats: make(map[string]int64),
		LastUpdate:    time.Now(),
	}

	// 初始化年度统计
	for year := config.StartYear; year <= config.EndYear; year++ {
		stats.YearlyStats[year] = &YearlyStats{Year: year}
	}

	engine := &CVEScanningEngine{
		name:                 "CVE扫描引擎",
		enabled:              true,
		config:               config,
		stats:                stats,
		logService:           logService,
		vulnService:          vulnService,
		vulnerabilityMatcher: NewVulnerabilityMatcher(),
		pocExecutor:          NewCVEPoCExecutor(),
		exploitDetector:      NewExploitDetector(),
		historicalScanner:    NewHistoricalCVEScanner(config),
		yearlyAnalyzer:       NewYearlyVulnerabilityAnalyzer(),
		trendAnalyzer:        NewVulnerabilityTrendAnalyzer(),
	}

	// 初始化CVE数据库
	engine.initializeCVEDatabase()

	// 初始化指纹识别引擎
	engine.initializeFingerprintEngine()

	logger.Infof("CVE扫描引擎初始化完成 - 支持年份范围: %d-%d",
		config.StartYear, config.EndYear)

	return engine
}

// GetName 获取引擎名称
func (e *CVEScanningEngine) GetName() string {
	return e.name
}

// GetType 获取引擎类型
func (e *CVEScanningEngine) GetType() string {
	return "cve"
}

// GetSupportedTargets 获取支持的目标类型
func (e *CVEScanningEngine) GetSupportedTargets() []string {
	return []string{"web", "api", "network", "host", "service"}
}

// IsEnabled 检查引擎是否启用
func (e *CVEScanningEngine) IsEnabled() bool {
	e.mutex.RLock()
	defer e.mutex.RUnlock()
	return e.enabled
}

// SetLogService 设置日志服务
func (e *CVEScanningEngine) SetLogService(logService *services.ScanLogService) {
	e.logService = logService
}

// Validate 验证扫描配置
func (e *CVEScanningEngine) Validate(config *types.ScanConfig) error {
	if config == nil {
		return fmt.Errorf("扫描配置不能为空")
	}

	if config.TaskID == "" {
		return fmt.Errorf("任务ID不能为空")
	}

	// 设置默认CVE配置
	if config.CVEConfig == nil {
		config.CVEConfig = &types.CVEScanConfig{
			EnableHistoricalScan:   true,
			StartYear:              2000,
			EndYear:                time.Now().Year(),
			EnablePoCVerification:  true,
			EnableExploitDetection: true,
			ScanStrategy:           "comprehensive",
			ConfidenceThreshold:    0.7,
			MaxCVEsPerTarget:       1000,
		}
	}

	return nil
}

// Scan 执行CVE扫描
func (e *CVEScanningEngine) Scan(ctx context.Context, target *types.ScanTarget, config *types.ScanConfig, progress chan<- *types.ScanProgress) (*types.ScanResult, error) {
	startTime := time.Now()

	// 发送初始进度
	select {
	case progress <- &types.ScanProgress{
		Stage:     "初始化",
		Progress:  0,
		Message:   "开始CVE扫描",
		Timestamp: time.Now(),
	}:
	case <-ctx.Done():
		return nil, ctx.Err()
	}

	// 验证配置
	if err := e.Validate(config); err != nil {
		return nil, fmt.Errorf("配置验证失败: %v", err)
	}

	// 创建扫描结果
	result := &types.ScanResult{
		TaskID:          config.TaskID,
		TargetID:        target.ID,
		StartTime:       startTime,
		Status:          "running",
		Vulnerabilities: make([]*types.Vulnerability, 0),
		Statistics:      &types.ScanStatistics{},
		Metadata:        make(map[string]interface{}),
		Errors:          make([]string, 0),
	}

	// 执行CVE扫描流程
	vulnerabilities, err := e.performCVEScan(ctx, target, config, progress)
	if err != nil {
		result.Status = "failed"
		result.Errors = append(result.Errors, err.Error())
		return result, err
	}

	// 设置扫描结果
	result.Vulnerabilities = vulnerabilities
	result.EndTime = time.Now()
	result.Duration = result.EndTime.Sub(result.StartTime)
	result.Status = "completed"

	// 更新统计信息
	e.updateScanStatistics(result)

	// 添加元数据
	e.addScanMetadata(result, config)

	// 发送完成进度
	select {
	case progress <- &types.ScanProgress{
		Stage:     "完成",
		Progress:  100,
		Message:   fmt.Sprintf("CVE扫描完成，发现 %d 个漏洞", len(vulnerabilities)),
		Timestamp: time.Now(),
	}:
	case <-ctx.Done():
		return result, ctx.Err()
	}

	logger.Infof("CVE扫描完成 - 目标: %s, 耗时: %v, 漏洞数: %d",
		target.Value, result.Duration, len(vulnerabilities))

	return result, nil
}

// performCVEScan 执行CVE扫描
func (e *CVEScanningEngine) performCVEScan(ctx context.Context, target *types.ScanTarget, config *types.ScanConfig, progress chan<- *types.ScanProgress) ([]*types.Vulnerability, error) {
	var allVulnerabilities []*types.Vulnerability

	// 阶段1: 指纹识别 (0-20%)
	select {
	case progress <- &types.ScanProgress{
		Stage:     "指纹识别",
		Progress:  10,
		Message:   "识别目标技术栈",
		Timestamp: time.Now(),
	}:
	case <-ctx.Done():
		return nil, ctx.Err()
	}

	technologies, err := e.performFingerprintIdentification(ctx, target)
	if err != nil {
		logger.Warnf("指纹识别失败: %v", err)
		// 继续执行，不因指纹识别失败而中断
	}

	// 阶段2: CVE匹配 (20-60%)
	select {
	case progress <- &types.ScanProgress{
		Stage:     "CVE匹配",
		Progress:  30,
		Message:   fmt.Sprintf("匹配 %d 个技术的CVE漏洞", len(technologies)),
		Timestamp: time.Now(),
	}:
	case <-ctx.Done():
		return nil, ctx.Err()
	}

	cveVulnerabilities, err := e.performCVEMatching(ctx, technologies, config, progress)
	if err != nil {
		return nil, fmt.Errorf("CVE匹配失败: %v", err)
	}
	allVulnerabilities = append(allVulnerabilities, cveVulnerabilities...)

	// 阶段3: 历史CVE扫描 (60-80%)
	if config.CVEConfig.EnableHistoricalScan {
		select {
		case progress <- &types.ScanProgress{
			Stage:     "历史CVE扫描",
			Progress:  70,
			Message:   fmt.Sprintf("扫描 %d-%d 年历史CVE", config.CVEConfig.StartYear, config.CVEConfig.EndYear),
			Timestamp: time.Now(),
		}:
		case <-ctx.Done():
			return nil, ctx.Err()
		}

		historicalVulns, err := e.performHistoricalCVEScan(ctx, target, technologies, config, progress)
		if err != nil {
			logger.Warnf("历史CVE扫描失败: %v", err)
		} else {
			allVulnerabilities = append(allVulnerabilities, historicalVulns...)
		}
	}

	// 阶段4: PoC验证 (80-90%)
	if config.CVEConfig.EnablePoCVerification {
		select {
		case progress <- &types.ScanProgress{
			Stage:     "PoC验证",
			Progress:  85,
			Message:   "验证漏洞可利用性",
			Timestamp: time.Now(),
		}:
		case <-ctx.Done():
			return nil, ctx.Err()
		}

		allVulnerabilities = e.performPoCVerification(ctx, target, allVulnerabilities, progress)
	}

	// 阶段5: 利用检测 (90-95%)
	if config.CVEConfig.EnableExploitDetection {
		select {
		case progress <- &types.ScanProgress{
			Stage:     "利用检测",
			Progress:  92,
			Message:   "检测公开利用代码",
			Timestamp: time.Now(),
		}:
		case <-ctx.Done():
			return nil, ctx.Err()
		}

		allVulnerabilities = e.performExploitDetection(ctx, allVulnerabilities, progress)
	}

	// 阶段6: 结果处理 (95-100%)
	select {
	case progress <- &types.ScanProgress{
		Stage:     "结果处理",
		Progress:  97,
		Message:   "处理和排序扫描结果",
		Timestamp: time.Now(),
	}:
	case <-ctx.Done():
		return nil, ctx.Err()
	}

	// 去重和排序
	allVulnerabilities = e.deduplicateAndSortVulnerabilities(allVulnerabilities)

	// 应用过滤器
	allVulnerabilities = e.applyFilters(allVulnerabilities, config)

	return allVulnerabilities, nil
}

// Stop 停止扫描
func (e *CVEScanningEngine) Stop(ctx context.Context, taskID string) error {
	logger.Infof("停止CVE扫描任务: %s", taskID)

	// 这里可以实现具体的停止逻辑
	// 例如取消正在进行的扫描、清理资源等

	return nil
}

// 初始化方法

// initializeCVEDatabase 初始化CVE数据库
func (e *CVEScanningEngine) initializeCVEDatabase() {
	// 创建CVE数据库配置
	dbConfig := &cve.CVEDatabaseConfig{
		DatabasePath:   "data/cve.db",
		EnableCache:    true,
		CacheSize:      10000,
		UpdateInterval: 24 * time.Hour,
	}

	// 初始化CVE数据库
	var err error
	e.cveDatabase, err = cve.NewCVEDatabase(dbConfig)
	if err != nil {
		logger.Errorf("CVE数据库初始化失败: %v", err)
		// 使用内存数据库作为备选
		e.cveDatabase = e.createInMemoryCVEDatabase()
	}

	// 创建CVE管理器配置
	managerConfig := &cve.CVEConfig{
		DataSources: []string{
			"https://nvd.nist.gov/feeds/json/cve/1.1/",
			"https://cve.mitre.org/data/downloads/",
		},
		UpdateInterval:   6 * time.Hour,
		CacheSize:        10000,
		EnableAutoUpdate: true,
		APIKeys:          make(map[string]string),
	}

	// 初始化CVE管理器
	e.cveManager = cve.NewCVEManager(managerConfig)

	// 特别加载2024年CVE数据
	e.load2024CVEData()

	// 特别加载2023年CVE数据
	e.load2023CVEData()

	// 特别加载2022年CVE数据
	e.load2022CVEData()

	// 特别加载2021年CVE数据
	e.load2021CVEData()

	// 特别加载2020年CVE数据
	e.load2020CVEData()

	// 特别加载2019年CVE数据
	e.load2019CVEData()

	// 特别加载2018年CVE数据
	e.load2018CVEData()

	// 特别加载2017年CVE数据
	e.load2017CVEData()

	// 特别加载2016年CVE数据
	e.load2016CVEData()

	// 特别加载2015年CVE数据
	e.load2015CVEData()

	// 特别加载2014年CVE数据
	e.load2014CVEData()

	// 特别加载2013年CVE数据
	e.load2013CVEData()

	// 特别加载2012年CVE数据
	e.load2012CVEData()

	// 特别加载2011年CVE数据
	e.load2011CVEData()

	// 特别加载2010年CVE数据
	e.load2010CVEData()

	// 特别加载2009年CVE数据
	e.load2009CVEData()

	// 特别加载2008年CVE数据
	e.load2008CVEData()

	// 特别加载2007年CVE数据
	e.load2007CVEData()

	// 特别加载2006年CVE数据
	e.load2006CVEData()

	// 特别加载2005年CVE数据
	e.load2005CVEData()

	// 特别加载2004年CVE数据
	e.load2004CVEData()

	// 特别加载2003年CVE数据
	e.load2003CVEData()

	// 特别加载2002年CVE数据
	e.load2002CVEData()

	// 特别加载2001年CVE数据
	e.load2001CVEData()

	// 优化CVE扫描性能
	e.optimizeCVEScanning()

	logger.Infof("CVE数据库和管理器初始化完成")
}

// initializeFingerprintEngine 初始化指纹识别引擎
func (e *CVEScanningEngine) initializeFingerprintEngine() {
	// 创建指纹识别引擎配置
	fpConfig := &fingerprint.FingerprintConfig{
		RequestTimeout:    30 * time.Second,
		MaxRedirects:      5,
		UserAgent:         "CVE-Scanner/1.0",
		EnableJSAnalysis:  true,
		EnableCSSAnalysis: true,
		MaxResponseSize:   1024 * 1024, // 1MB
	}

	// 创建指纹识别引擎
	e.fingerprintEngine = fingerprint.NewFingerprintEngine(fpConfig)

	logger.Infof("指纹识别引擎初始化完成")
}

// createInMemoryCVEDatabase 创建内存CVE数据库
func (e *CVEScanningEngine) createInMemoryCVEDatabase() *cve.CVEDatabase {
	// 这里创建一个简化的内存CVE数据库
	// 在实际实现中，这里会加载内置的CVE数据
	logger.Warnf("使用内存CVE数据库作为备选方案")
	return nil // 简化实现
}

// load2024CVEData 加载2024年CVE数据
func (e *CVEScanningEngine) load2024CVEData() {
	logger.Info("开始加载2024年CVE数据...")

	// 创建本地CVE管理器用于加载2024年数据
	localCVEManager := NewCVEManager(&CVEManagerConfig{
		CVEDirectory:   "rules/cve",
		Enable2024CVEs: true,
		MinSeverity:    0.0,
		MaxAge:         1, // 只加载最近1年的数据
		CacheEnabled:   true,
	})

	// 加载2024年CVE数据
	if err := localCVEManager.Load2024CVEs(); err != nil {
		logger.Errorf("加载2024年CVE数据失败: %v", err)
		return
	}

	// 获取2024年CVE统计信息
	stats2024 := localCVEManager.Get2024CVEStats()
	logger.Infof("2024年CVE数据加载完成: %v", stats2024)

	// 更新引擎统计信息
	e.update2024CVEStats(stats2024)
}

// load2023CVEData 加载2023年CVE数据
func (e *CVEScanningEngine) load2023CVEData() {
	logger.Info("开始加载2023年CVE数据...")

	// 创建本地CVE管理器用于加载2023年数据
	localCVEManager := NewCVEManager(&CVEManagerConfig{
		CVEDirectory:   "rules/cve",
		Enable2023CVEs: true,
		MinSeverity:    0.0,
		MaxAge:         2, // 加载最近2年的数据
		CacheEnabled:   true,
	})

	// 加载2023年CVE数据
	if err := localCVEManager.Load2023CVEs(); err != nil {
		logger.Errorf("加载2023年CVE数据失败: %v", err)
		return
	}

	// 获取2023年CVE统计信息
	stats2023 := localCVEManager.Get2023CVEStats()
	logger.Infof("2023年CVE数据加载完成: %v", stats2023)

	// 更新引擎统计信息
	e.update2023CVEStats(stats2023)
}

// load2022CVEData 加载2022年CVE数据
func (e *CVEScanningEngine) load2022CVEData() {
	logger.Info("开始加载2022年CVE数据...")

	// 创建本地CVE管理器用于加载2022年数据
	localCVEManager := NewCVEManager(&CVEManagerConfig{
		CVEDirectory:   "rules/cve",
		Enable2022CVEs: true,
		MinSeverity:    0.0,
		MaxAge:         3, // 加载最近3年的数据
		CacheEnabled:   true,
	})

	// 加载2022年CVE数据
	if err := localCVEManager.Load2022CVEs(); err != nil {
		logger.Errorf("加载2022年CVE数据失败: %v", err)
		return
	}

	// 获取2022年CVE统计信息
	stats2022 := localCVEManager.Get2022CVEStats()
	logger.Infof("2022年CVE数据加载完成: %v", stats2022)

	// 更新引擎统计信息
	e.update2022CVEStats(stats2022)
}

// load2021CVEData 加载2021年CVE数据
func (e *CVEScanningEngine) load2021CVEData() {
	logger.Info("开始加载2021年CVE数据...")

	// 创建本地CVE管理器用于加载2021年数据
	localCVEManager := NewCVEManager(&CVEManagerConfig{
		CVEDirectory:   "rules/cve",
		Enable2021CVEs: true,
		MinSeverity:    0.0,
		MaxAge:         4, // 加载最近4年的数据
		CacheEnabled:   true,
	})

	// 加载2021年CVE数据
	if err := localCVEManager.Load2021CVEs(); err != nil {
		logger.Errorf("加载2021年CVE数据失败: %v", err)
		return
	}

	// 获取2021年CVE统计信息
	stats2021 := localCVEManager.Get2021CVEStats()
	logger.Infof("2021年CVE数据加载完成: %v", stats2021)

	// 更新引擎统计信息
	e.update2021CVEStats(stats2021)
}

// load2020CVEData 加载2020年CVE数据
func (e *CVEScanningEngine) load2020CVEData() {
	logger.Info("开始加载2020年CVE数据...")

	// 创建本地CVE管理器用于加载2020年数据
	localCVEManager := NewCVEManager(&CVEManagerConfig{
		CVEDirectory:   "rules/cve",
		Enable2020CVEs: true,
		MinSeverity:    0.0,
		MaxAge:         5, // 加载最近5年的数据
		CacheEnabled:   true,
	})

	// 加载2020年CVE数据
	if err := localCVEManager.Load2020CVEs(); err != nil {
		logger.Errorf("加载2020年CVE数据失败: %v", err)
		return
	}

	// 获取2020年CVE统计信息
	stats2020 := localCVEManager.Get2020CVEStats()
	logger.Infof("2020年CVE数据加载完成: %v", stats2020)

	// 更新引擎统计信息
	e.update2020CVEStats(stats2020)
}

// load2019CVEData 加载2019年CVE数据
func (e *CVEScanningEngine) load2019CVEData() {
	logger.Info("开始加载2019年CVE数据...")

	// 创建本地CVE管理器用于加载2019年数据
	localCVEManager := NewCVEManager(&CVEManagerConfig{
		CVEDirectory:   "rules/cve",
		Enable2019CVEs: true,
		MinSeverity:    0.0,
		MaxAge:         6, // 加载最近6年的数据
		CacheEnabled:   true,
	})

	// 加载2019年CVE数据
	if err := localCVEManager.Load2019CVEs(); err != nil {
		logger.Errorf("加载2019年CVE数据失败: %v", err)
		return
	}

	// 获取2019年CVE统计信息
	stats2019 := localCVEManager.Get2019CVEStats()
	logger.Infof("2019年CVE数据加载完成: %v", stats2019)

	// 更新引擎统计信息
	e.update2019CVEStats(stats2019)
}

// load2018CVEData 加载2018年CVE数据
func (e *CVEScanningEngine) load2018CVEData() {
	logger.Info("开始加载2018年CVE数据...")

	// 创建本地CVE管理器用于加载2018年数据
	localCVEManager := NewCVEManager(&CVEManagerConfig{
		CVEDirectory:   "rules/cve",
		Enable2018CVEs: true,
		MinSeverity:    0.0,
		MaxAge:         7, // 加载最近7年的数据
		CacheEnabled:   true,
	})

	// 加载2018年CVE数据
	if err := localCVEManager.Load2018CVEs(); err != nil {
		logger.Errorf("加载2018年CVE数据失败: %v", err)
		return
	}

	// 获取2018年CVE统计信息
	stats2018 := localCVEManager.Get2018CVEStats()
	logger.Infof("2018年CVE数据加载完成: %v", stats2018)

	// 更新引擎统计信息
	e.update2018CVEStats(stats2018)
}

// load2017CVEData 加载2017年CVE数据
func (e *CVEScanningEngine) load2017CVEData() {
	logger.Info("开始加载2017年CVE数据...")

	// 创建本地CVE管理器用于加载2017年数据
	localCVEManager := NewCVEManager(&CVEManagerConfig{
		CVEDirectory:   "rules/cve",
		Enable2017CVEs: true,
		MinSeverity:    0.0,
		MaxAge:         8, // 加载最近8年的数据
		CacheEnabled:   true,
	})

	// 加载2017年CVE数据
	if err := localCVEManager.Load2017CVEs(); err != nil {
		logger.Errorf("加载2017年CVE数据失败: %v", err)
		return
	}

	// 获取2017年CVE统计信息
	stats2017 := localCVEManager.Get2017CVEStats()
	logger.Infof("2017年CVE数据加载完成: %v", stats2017)

	// 更新引擎统计信息
	e.update2017CVEStats(stats2017)
}

// load2016CVEData 加载2016年CVE数据
func (e *CVEScanningEngine) load2016CVEData() {
	logger.Info("开始加载2016年CVE数据...")

	// 创建本地CVE管理器用于加载2016年数据
	localCVEManager := NewCVEManager(&CVEManagerConfig{
		CVEDirectory:   "rules/cve",
		Enable2016CVEs: true,
		MinSeverity:    0.0,
		MaxAge:         9, // 加载最近9年的数据
		CacheEnabled:   true,
	})

	// 加载2016年CVE数据
	if err := localCVEManager.Load2016CVEs(); err != nil {
		logger.Errorf("加载2016年CVE数据失败: %v", err)
		return
	}

	// 获取2016年CVE统计信息
	stats2016 := localCVEManager.Get2016CVEStats()
	logger.Infof("2016年CVE数据加载完成: %v", stats2016)

	// 更新引擎统计信息
	e.update2016CVEStats(stats2016)
}

// load2015CVEData 加载2015年CVE数据
func (e *CVEScanningEngine) load2015CVEData() {
	logger.Info("开始加载2015年CVE数据...")

	// 创建本地CVE管理器用于加载2015年数据
	localCVEManager := NewCVEManager(&CVEManagerConfig{
		CVEDirectory:   "rules/cve",
		Enable2015CVEs: true,
		MinSeverity:    0.0,
		MaxAge:         10, // 加载最近10年的数据
		CacheEnabled:   true,
	})

	// 加载2015年CVE数据
	if err := localCVEManager.Load2015CVEs(); err != nil {
		logger.Errorf("加载2015年CVE数据失败: %v", err)
		return
	}

	// 获取2015年CVE统计信息
	stats2015 := localCVEManager.Get2015CVEStats()
	logger.Infof("2015年CVE数据加载完成: %v", stats2015)

	// 更新引擎统计信息
	e.update2015CVEStats(stats2015)
}

// load2014CVEData 加载2014年CVE数据
func (e *CVEScanningEngine) load2014CVEData() {
	logger.Info("开始加载2014年CVE数据...")

	// 创建本地CVE管理器用于加载2014年数据
	localCVEManager := NewCVEManager(&CVEManagerConfig{
		CVEDirectory:   "rules/cve",
		Enable2014CVEs: true,
		MinSeverity:    0.0,
		MaxAge:         11, // 加载最近11年的数据
		CacheEnabled:   true,
	})

	// 加载2014年CVE数据
	if err := localCVEManager.Load2014CVEs(); err != nil {
		logger.Errorf("加载2014年CVE数据失败: %v", err)
		return
	}

	// 获取2014年CVE统计信息
	stats2014 := localCVEManager.Get2014CVEStats()
	logger.Infof("2014年CVE数据加载完成: %v", stats2014)

	// 更新引擎统计信息
	e.update2014CVEStats(stats2014)
}

// load2013CVEData 加载2013年CVE数据
func (e *CVEScanningEngine) load2013CVEData() {
	logger.Info("开始加载2013年CVE数据...")

	// 创建本地CVE管理器用于加载2013年数据
	localCVEManager := NewCVEManager(&CVEManagerConfig{
		CVEDirectory:   "rules/cve",
		Enable2013CVEs: true,
		MinSeverity:    0.0,
		MaxAge:         12, // 加载最近12年的数据
		CacheEnabled:   true,
	})

	// 加载2013年CVE数据
	if err := localCVEManager.Load2013CVEs(); err != nil {
		logger.Errorf("加载2013年CVE数据失败: %v", err)
		return
	}

	// 获取2013年CVE统计信息
	stats2013 := localCVEManager.Get2013CVEStats()
	logger.Infof("2013年CVE数据加载完成: %v", stats2013)

	// 更新引擎统计信息
	e.update2013CVEStats(stats2013)
}

// load2012CVEData 加载2012年CVE数据
func (e *CVEScanningEngine) load2012CVEData() {
	logger.Info("开始加载2012年CVE数据...")

	// 创建本地CVE管理器用于加载2012年数据
	localCVEManager := NewCVEManager(&CVEManagerConfig{
		CVEDirectory:   "rules/cve",
		Enable2012CVEs: true,
		MinSeverity:    0.0,
		MaxAge:         13, // 加载最近13年的数据
		CacheEnabled:   true,
	})

	// 加载2012年CVE数据
	if err := localCVEManager.Load2012CVEs(); err != nil {
		logger.Errorf("加载2012年CVE数据失败: %v", err)
		return
	}

	// 获取2012年CVE统计信息
	stats2012 := localCVEManager.Get2012CVEStats()
	logger.Infof("2012年CVE数据加载完成: %v", stats2012)

	// 更新引擎统计信息
	e.update2012CVEStats(stats2012)
}

// load2011CVEData 加载2011年CVE数据
func (e *CVEScanningEngine) load2011CVEData() {
	logger.Info("开始加载2011年CVE数据...")

	// 创建本地CVE管理器用于加载2011年数据
	localCVEManager := NewCVEManager(&CVEManagerConfig{
		CVEDirectory:   "rules/cve",
		Enable2011CVEs: true,
		MinSeverity:    0.0,
		MaxAge:         14, // 加载最近14年的数据
		CacheEnabled:   true,
	})

	// 加载2011年CVE数据
	if err := localCVEManager.Load2011CVEs(); err != nil {
		logger.Errorf("加载2011年CVE数据失败: %v", err)
		return
	}

	// 获取2011年CVE统计信息
	stats2011 := localCVEManager.Get2011CVEStats()
	logger.Infof("2011年CVE数据加载完成: %v", stats2011)

	// 更新引擎统计信息
	e.update2011CVEStats(stats2011)
}

// load2010CVEData 加载2010年CVE数据
func (e *CVEScanningEngine) load2010CVEData() {
	logger.Info("开始加载2010年CVE数据...")

	// 创建本地CVE管理器用于加载2010年数据
	localCVEManager := NewCVEManager(&CVEManagerConfig{
		CVEDirectory:   "rules/cve",
		Enable2010CVEs: true,
		MinSeverity:    0.0,
		MaxAge:         15, // 加载最近15年的数据
		CacheEnabled:   true,
	})

	// 加载2010年CVE数据
	if err := localCVEManager.Load2010CVEs(); err != nil {
		logger.Errorf("加载2010年CVE数据失败: %v", err)
		return
	}

	// 获取2010年CVE统计信息
	stats2010 := localCVEManager.Get2010CVEStats()
	logger.Infof("2010年CVE数据加载完成: %v", stats2010)

	// 更新引擎统计信息
	e.update2010CVEStats(stats2010)
}

// load2009CVEData 加载2009年CVE数据
func (e *CVEScanningEngine) load2009CVEData() {
	logger.Info("开始加载2009年CVE数据...")

	// 创建本地CVE管理器用于加载2009年数据
	localCVEManager := NewCVEManager(&CVEManagerConfig{
		CVEDirectory:   "rules/cve",
		Enable2009CVEs: true,
		MinSeverity:    0.0,
		MaxAge:         16, // 加载最近16年的数据
		CacheEnabled:   true,
	})

	// 加载2009年CVE数据
	if err := localCVEManager.Load2009CVEs(); err != nil {
		logger.Errorf("加载2009年CVE数据失败: %v", err)
		return
	}

	// 获取2009年CVE统计信息
	stats2009 := localCVEManager.Get2009CVEStats()
	logger.Infof("2009年CVE数据加载完成: %v", stats2009)

	// 更新引擎统计信息
	e.update2009CVEStats(stats2009)
}

// load2008CVEData 加载2008年CVE数据
func (e *CVEScanningEngine) load2008CVEData() {
	logger.Info("开始加载2008年CVE数据...")

	// 创建本地CVE管理器用于加载2008年数据
	localCVEManager := NewCVEManager(&CVEManagerConfig{
		CVEDirectory:   "rules/cve",
		Enable2008CVEs: true,
		MinSeverity:    0.0,
		MaxAge:         17, // 加载最近17年的数据
		CacheEnabled:   true,
	})

	// 加载2008年CVE数据
	if err := localCVEManager.Load2008CVEs(); err != nil {
		logger.Errorf("加载2008年CVE数据失败: %v", err)
		return
	}

	// 获取2008年CVE统计信息
	stats2008 := localCVEManager.Get2008CVEStats()
	logger.Infof("2008年CVE数据加载完成: %v", stats2008)

	// 更新引擎统计信息
	e.update2008CVEStats(stats2008)
}

// load2007CVEData 加载2007年CVE数据
func (e *CVEScanningEngine) load2007CVEData() {
	logger.Info("开始加载2007年CVE数据...")

	// 创建本地CVE管理器用于加载2007年数据
	localCVEManager := NewCVEManager(&CVEManagerConfig{
		CVEDirectory:   "rules/cve",
		Enable2007CVEs: true,
		MinSeverity:    0.0,
		MaxAge:         18, // 加载最近18年的数据
		CacheEnabled:   true,
	})

	// 加载2007年CVE数据
	if err := localCVEManager.Load2007CVEs(); err != nil {
		logger.Errorf("加载2007年CVE数据失败: %v", err)
		return
	}

	// 获取2007年CVE统计信息
	stats2007 := localCVEManager.Get2007CVEStats()
	logger.Infof("2007年CVE数据加载完成: %v", stats2007)

	// 更新引擎统计信息
	e.update2007CVEStats(stats2007)
}

// load2006CVEData 加载2006年CVE数据
func (e *CVEScanningEngine) load2006CVEData() {
	logger.Info("开始加载2006年CVE数据...")

	// 创建本地CVE管理器用于加载2006年数据
	localCVEManager := NewCVEManager(&CVEManagerConfig{
		CVEDirectory:   "rules/cve",
		Enable2006CVEs: true,
		MinSeverity:    0.0,
		MaxAge:         19, // 加载最近19年的数据
		CacheEnabled:   true,
	})

	// 加载2006年CVE数据
	if err := localCVEManager.Load2006CVEs(); err != nil {
		logger.Errorf("加载2006年CVE数据失败: %v", err)
		return
	}

	// 获取2006年CVE统计信息
	stats2006 := localCVEManager.Get2006CVEStats()
	logger.Infof("2006年CVE数据加载完成: %v", stats2006)

	// 更新引擎统计信息
	e.update2006CVEStats(stats2006)
}

// load2005CVEData 加载2005年CVE数据
func (e *CVEScanningEngine) load2005CVEData() {
	logger.Info("开始加载2005年CVE数据...")

	// 创建本地CVE管理器用于加载2005年数据
	localCVEManager := NewCVEManager(&CVEManagerConfig{
		CVEDirectory:   "rules/cve",
		Enable2005CVEs: true,
		MinSeverity:    0.0,
		MaxAge:         20, // 加载最近20年的数据
		CacheEnabled:   true,
	})

	// 加载2005年CVE数据
	if err := localCVEManager.Load2005CVEs(); err != nil {
		logger.Errorf("加载2005年CVE数据失败: %v", err)
		return
	}

	// 获取2005年CVE统计信息
	stats2005 := localCVEManager.Get2005CVEStats()
	logger.Infof("2005年CVE数据加载完成: %v", stats2005)

	// 更新引擎统计信息
	e.update2005CVEStats(stats2005)
}

// load2004CVEData 加载2004年CVE数据
func (e *CVEScanningEngine) load2004CVEData() {
	logger.Info("开始加载2004年CVE数据...")

	// 创建本地CVE管理器用于加载2004年数据
	localCVEManager := NewCVEManager(&CVEManagerConfig{
		CVEDirectory:   "rules/cve",
		Enable2004CVEs: true,
		MinSeverity:    0.0,
		MaxAge:         21, // 加载最近21年的数据
		CacheEnabled:   true,
	})

	// 加载2004年CVE数据
	if err := localCVEManager.Load2004CVEs(); err != nil {
		logger.Errorf("加载2004年CVE数据失败: %v", err)
		return
	}

	// 获取2004年CVE统计信息
	stats2004 := localCVEManager.Get2004CVEStats()
	logger.Infof("2004年CVE数据加载完成: %v", stats2004)

	// 更新引擎统计信息
	e.update2004CVEStats(stats2004)
}

// load2003CVEData 加载2003年CVE数据
func (e *CVEScanningEngine) load2003CVEData() {
	logger.Info("开始加载2003年CVE数据...")

	// 创建本地CVE管理器用于加载2003年数据
	localCVEManager := NewCVEManager(&CVEManagerConfig{
		CVEDirectory:   "rules/cve",
		Enable2003CVEs: true,
		MinSeverity:    0.0,
		MaxAge:         22, // 加载最近22年的数据
		CacheEnabled:   true,
	})

	// 加载2003年CVE数据
	if err := localCVEManager.Load2003CVEs(); err != nil {
		logger.Errorf("加载2003年CVE数据失败: %v", err)
		return
	}

	// 获取2003年CVE统计信息
	stats2003 := localCVEManager.Get2003CVEStats()
	logger.Infof("2003年CVE数据加载完成: %v", stats2003)

	// 更新引擎统计信息
	e.update2003CVEStats(stats2003)
}

// load2002CVEData 加载2002年CVE数据
func (e *CVEScanningEngine) load2002CVEData() {
	logger.Info("开始加载2002年CVE数据...")

	// 创建本地CVE管理器用于加载2002年数据
	localCVEManager := NewCVEManager(&CVEManagerConfig{
		CVEDirectory:   "rules/cve",
		Enable2002CVEs: true,
		MinSeverity:    0.0,
		MaxAge:         23, // 加载最近23年的数据
		CacheEnabled:   true,
	})

	// 加载2002年CVE数据
	if err := localCVEManager.Load2002CVEs(); err != nil {
		logger.Errorf("加载2002年CVE数据失败: %v", err)
		return
	}

	// 获取2002年CVE统计信息
	stats2002 := localCVEManager.Get2002CVEStats()
	logger.Infof("2002年CVE数据加载完成: %v", stats2002)

	// 更新引擎统计信息
	e.update2002CVEStats(stats2002)
}

// load2001CVEData 加载2001年CVE数据
func (e *CVEScanningEngine) load2001CVEData() {
	logger.Info("开始加载2001年CVE数据...")

	// 创建本地CVE管理器用于加载2001年数据
	localCVEManager := NewCVEManager(&CVEManagerConfig{
		CVEDirectory:   "rules/cve",
		Enable2001CVEs: true,
		MinSeverity:    0.0,
		MaxAge:         24, // 加载最近24年的数据
		CacheEnabled:   true,
	})

	// 加载2001年CVE数据
	if err := localCVEManager.Load2001CVEs(); err != nil {
		logger.Errorf("加载2001年CVE数据失败: %v", err)
		return
	}

	// 获取2001年CVE统计信息
	stats2001 := localCVEManager.Get2001CVEStats()
	logger.Infof("2001年CVE数据加载完成: %v", stats2001)

	// 更新引擎统计信息
	e.update2001CVEStats(stats2001)
}

// update2001CVEStats 更新2001年CVE统计信息
func (e *CVEScanningEngine) update2001CVEStats(stats map[string]interface{}) {
	e.mutex.Lock()
	defer e.mutex.Unlock()

	if total, ok := stats["total"].(int); ok && total > 0 {
		// 更新年度统计
		if e.stats.YearlyStats[2001] == nil {
			e.stats.YearlyStats[2001] = &YearlyStats{Year: 2001}
		}
		e.stats.YearlyStats[2001].CVEsScanned = int64(total)

		// 更新严重程度统计
		if severityStats, ok := stats["by_severity"].(map[string]int); ok {
			for severity, count := range severityStats {
				e.stats.SeverityStats[severity] += int64(count)
			}
		}

		// 更新最后更新时间
		e.stats.LastUpdate = time.Now()

		logger.Infof("2001年CVE统计信息已更新 - 总数: %d", total)

		// 记录著名漏洞
		if famousCVEs, ok := stats["famous_cves"].([]string); ok && len(famousCVEs) > 0 {
			logger.Infof("发现2001年著名漏洞: %v", famousCVEs)
		}
	}
}

// update2002CVEStats 更新2002年CVE统计信息
func (e *CVEScanningEngine) update2002CVEStats(stats map[string]interface{}) {
	e.mutex.Lock()
	defer e.mutex.Unlock()

	if total, ok := stats["total"].(int); ok && total > 0 {
		// 更新年度统计
		if e.stats.YearlyStats[2002] == nil {
			e.stats.YearlyStats[2002] = &YearlyStats{Year: 2002}
		}
		e.stats.YearlyStats[2002].CVEsScanned = int64(total)

		// 更新严重程度统计
		if severityStats, ok := stats["by_severity"].(map[string]int); ok {
			for severity, count := range severityStats {
				e.stats.SeverityStats[severity] += int64(count)
			}
		}

		// 更新最后更新时间
		e.stats.LastUpdate = time.Now()

		logger.Infof("2002年CVE统计信息已更新 - 总数: %d", total)

		// 记录著名漏洞
		if famousCVEs, ok := stats["famous_cves"].([]string); ok && len(famousCVEs) > 0 {
			logger.Infof("发现2002年著名漏洞: %v", famousCVEs)
		}
	}
}

// update2003CVEStats 更新2003年CVE统计信息
func (e *CVEScanningEngine) update2003CVEStats(stats map[string]interface{}) {
	e.mutex.Lock()
	defer e.mutex.Unlock()

	if total, ok := stats["total"].(int); ok && total > 0 {
		// 更新年度统计
		if e.stats.YearlyStats[2003] == nil {
			e.stats.YearlyStats[2003] = &YearlyStats{Year: 2003}
		}
		e.stats.YearlyStats[2003].CVEsScanned = int64(total)

		// 更新严重程度统计
		if severityStats, ok := stats["by_severity"].(map[string]int); ok {
			for severity, count := range severityStats {
				e.stats.SeverityStats[severity] += int64(count)
			}
		}

		// 更新最后更新时间
		e.stats.LastUpdate = time.Now()

		logger.Infof("2003年CVE统计信息已更新 - 总数: %d", total)

		// 记录著名漏洞
		if famousCVEs, ok := stats["famous_cves"].([]string); ok && len(famousCVEs) > 0 {
			logger.Infof("发现2003年著名漏洞: %v", famousCVEs)
		}
	}
}

// update2004CVEStats 更新2004年CVE统计信息
func (e *CVEScanningEngine) update2004CVEStats(stats map[string]interface{}) {
	e.mutex.Lock()
	defer e.mutex.Unlock()

	if total, ok := stats["total"].(int); ok && total > 0 {
		// 更新年度统计
		if e.stats.YearlyStats[2004] == nil {
			e.stats.YearlyStats[2004] = &YearlyStats{Year: 2004}
		}
		e.stats.YearlyStats[2004].CVEsScanned = int64(total)

		// 更新严重程度统计
		if severityStats, ok := stats["by_severity"].(map[string]int); ok {
			for severity, count := range severityStats {
				e.stats.SeverityStats[severity] += int64(count)
			}
		}

		// 更新最后更新时间
		e.stats.LastUpdate = time.Now()

		logger.Infof("2004年CVE统计信息已更新 - 总数: %d", total)

		// 记录著名漏洞
		if famousCVEs, ok := stats["famous_cves"].([]string); ok && len(famousCVEs) > 0 {
			logger.Infof("发现2004年著名漏洞: %v", famousCVEs)
		}
	}
}

// update2005CVEStats 更新2005年CVE统计信息
func (e *CVEScanningEngine) update2005CVEStats(stats map[string]interface{}) {
	e.mutex.Lock()
	defer e.mutex.Unlock()

	if total, ok := stats["total"].(int); ok && total > 0 {
		// 更新年度统计
		if e.stats.YearlyStats[2005] == nil {
			e.stats.YearlyStats[2005] = &YearlyStats{Year: 2005}
		}
		e.stats.YearlyStats[2005].CVEsScanned = int64(total)

		// 更新严重程度统计
		if severityStats, ok := stats["by_severity"].(map[string]int); ok {
			for severity, count := range severityStats {
				e.stats.SeverityStats[severity] += int64(count)
			}
		}

		// 更新最后更新时间
		e.stats.LastUpdate = time.Now()

		logger.Infof("2005年CVE统计信息已更新 - 总数: %d", total)

		// 记录著名漏洞
		if famousCVEs, ok := stats["famous_cves"].([]string); ok && len(famousCVEs) > 0 {
			logger.Infof("发现2005年著名漏洞: %v", famousCVEs)
		}
	}
}

// update2006CVEStats 更新2006年CVE统计信息
func (e *CVEScanningEngine) update2006CVEStats(stats map[string]interface{}) {
	e.mutex.Lock()
	defer e.mutex.Unlock()

	if total, ok := stats["total"].(int); ok && total > 0 {
		// 更新年度统计
		if e.stats.YearlyStats[2006] == nil {
			e.stats.YearlyStats[2006] = &YearlyStats{Year: 2006}
		}
		e.stats.YearlyStats[2006].CVEsScanned = int64(total)

		// 更新严重程度统计
		if severityStats, ok := stats["by_severity"].(map[string]int); ok {
			for severity, count := range severityStats {
				e.stats.SeverityStats[severity] += int64(count)
			}
		}

		// 更新最后更新时间
		e.stats.LastUpdate = time.Now()

		logger.Infof("2006年CVE统计信息已更新 - 总数: %d", total)

		// 记录著名漏洞
		if famousCVEs, ok := stats["famous_cves"].([]string); ok && len(famousCVEs) > 0 {
			logger.Infof("发现2006年著名漏洞: %v", famousCVEs)
		}
	}
}

// update2007CVEStats 更新2007年CVE统计信息
func (e *CVEScanningEngine) update2007CVEStats(stats map[string]interface{}) {
	e.mutex.Lock()
	defer e.mutex.Unlock()

	if total, ok := stats["total"].(int); ok && total > 0 {
		// 更新年度统计
		if e.stats.YearlyStats[2007] == nil {
			e.stats.YearlyStats[2007] = &YearlyStats{Year: 2007}
		}
		e.stats.YearlyStats[2007].CVEsScanned = int64(total)

		// 更新严重程度统计
		if severityStats, ok := stats["by_severity"].(map[string]int); ok {
			for severity, count := range severityStats {
				e.stats.SeverityStats[severity] += int64(count)
			}
		}

		// 更新最后更新时间
		e.stats.LastUpdate = time.Now()

		logger.Infof("2007年CVE统计信息已更新 - 总数: %d", total)

		// 记录著名漏洞
		if famousCVEs, ok := stats["famous_cves"].([]string); ok && len(famousCVEs) > 0 {
			logger.Infof("发现2007年著名漏洞: %v", famousCVEs)
		}
	}
}

// update2008CVEStats 更新2008年CVE统计信息
func (e *CVEScanningEngine) update2008CVEStats(stats map[string]interface{}) {
	e.mutex.Lock()
	defer e.mutex.Unlock()

	if total, ok := stats["total"].(int); ok && total > 0 {
		// 更新年度统计
		if e.stats.YearlyStats[2008] == nil {
			e.stats.YearlyStats[2008] = &YearlyStats{Year: 2008}
		}
		e.stats.YearlyStats[2008].CVEsScanned = int64(total)

		// 更新严重程度统计
		if severityStats, ok := stats["by_severity"].(map[string]int); ok {
			for severity, count := range severityStats {
				e.stats.SeverityStats[severity] += int64(count)
			}
		}

		// 更新最后更新时间
		e.stats.LastUpdate = time.Now()

		logger.Infof("2008年CVE统计信息已更新 - 总数: %d", total)

		// 记录著名漏洞
		if famousCVEs, ok := stats["famous_cves"].([]string); ok && len(famousCVEs) > 0 {
			logger.Infof("发现2008年著名漏洞: %v", famousCVEs)
		}
	}
}

// update2009CVEStats 更新2009年CVE统计信息
func (e *CVEScanningEngine) update2009CVEStats(stats map[string]interface{}) {
	e.mutex.Lock()
	defer e.mutex.Unlock()

	if total, ok := stats["total"].(int); ok && total > 0 {
		// 更新年度统计
		if e.stats.YearlyStats[2009] == nil {
			e.stats.YearlyStats[2009] = &YearlyStats{Year: 2009}
		}
		e.stats.YearlyStats[2009].CVEsScanned = int64(total)

		// 更新严重程度统计
		if severityStats, ok := stats["by_severity"].(map[string]int); ok {
			for severity, count := range severityStats {
				e.stats.SeverityStats[severity] += int64(count)
			}
		}

		// 更新最后更新时间
		e.stats.LastUpdate = time.Now()

		logger.Infof("2009年CVE统计信息已更新 - 总数: %d", total)

		// 记录著名漏洞
		if famousCVEs, ok := stats["famous_cves"].([]string); ok && len(famousCVEs) > 0 {
			logger.Infof("发现2009年著名漏洞: %v", famousCVEs)
		}
	}
}

// update2010CVEStats 更新2010年CVE统计信息
func (e *CVEScanningEngine) update2010CVEStats(stats map[string]interface{}) {
	e.mutex.Lock()
	defer e.mutex.Unlock()

	if total, ok := stats["total"].(int); ok && total > 0 {
		// 更新年度统计
		if e.stats.YearlyStats[2010] == nil {
			e.stats.YearlyStats[2010] = &YearlyStats{Year: 2010}
		}
		e.stats.YearlyStats[2010].CVEsScanned = int64(total)

		// 更新严重程度统计
		if severityStats, ok := stats["by_severity"].(map[string]int); ok {
			for severity, count := range severityStats {
				e.stats.SeverityStats[severity] += int64(count)
			}
		}

		// 更新最后更新时间
		e.stats.LastUpdate = time.Now()

		logger.Infof("2010年CVE统计信息已更新 - 总数: %d", total)

		// 记录著名漏洞
		if famousCVEs, ok := stats["famous_cves"].([]string); ok && len(famousCVEs) > 0 {
			logger.Infof("发现2010年著名漏洞: %v", famousCVEs)
		}
	}
}

// update2011CVEStats 更新2011年CVE统计信息
func (e *CVEScanningEngine) update2011CVEStats(stats map[string]interface{}) {
	e.mutex.Lock()
	defer e.mutex.Unlock()

	if total, ok := stats["total"].(int); ok && total > 0 {
		// 更新年度统计
		if e.stats.YearlyStats[2011] == nil {
			e.stats.YearlyStats[2011] = &YearlyStats{Year: 2011}
		}
		e.stats.YearlyStats[2011].CVEsScanned = int64(total)

		// 更新严重程度统计
		if severityStats, ok := stats["by_severity"].(map[string]int); ok {
			for severity, count := range severityStats {
				e.stats.SeverityStats[severity] += int64(count)
			}
		}

		// 更新最后更新时间
		e.stats.LastUpdate = time.Now()

		logger.Infof("2011年CVE统计信息已更新 - 总数: %d", total)

		// 记录著名漏洞
		if famousCVEs, ok := stats["famous_cves"].([]string); ok && len(famousCVEs) > 0 {
			logger.Infof("发现2011年著名漏洞: %v", famousCVEs)
		}
	}
}

// update2012CVEStats 更新2012年CVE统计信息
func (e *CVEScanningEngine) update2012CVEStats(stats map[string]interface{}) {
	e.mutex.Lock()
	defer e.mutex.Unlock()

	if total, ok := stats["total"].(int); ok && total > 0 {
		// 更新年度统计
		if e.stats.YearlyStats[2012] == nil {
			e.stats.YearlyStats[2012] = &YearlyStats{Year: 2012}
		}
		e.stats.YearlyStats[2012].CVEsScanned = int64(total)

		// 更新严重程度统计
		if severityStats, ok := stats["by_severity"].(map[string]int); ok {
			for severity, count := range severityStats {
				e.stats.SeverityStats[severity] += int64(count)
			}
		}

		// 更新最后更新时间
		e.stats.LastUpdate = time.Now()

		logger.Infof("2012年CVE统计信息已更新 - 总数: %d", total)

		// 记录著名漏洞
		if famousCVEs, ok := stats["famous_cves"].([]string); ok && len(famousCVEs) > 0 {
			logger.Infof("发现2012年著名漏洞: %v", famousCVEs)
		}
	}
}

// update2013CVEStats 更新2013年CVE统计信息
func (e *CVEScanningEngine) update2013CVEStats(stats map[string]interface{}) {
	e.mutex.Lock()
	defer e.mutex.Unlock()

	if total, ok := stats["total"].(int); ok && total > 0 {
		// 更新年度统计
		if e.stats.YearlyStats[2013] == nil {
			e.stats.YearlyStats[2013] = &YearlyStats{Year: 2013}
		}
		e.stats.YearlyStats[2013].CVEsScanned = int64(total)

		// 更新严重程度统计
		if severityStats, ok := stats["by_severity"].(map[string]int); ok {
			for severity, count := range severityStats {
				e.stats.SeverityStats[severity] += int64(count)
			}
		}

		// 更新最后更新时间
		e.stats.LastUpdate = time.Now()

		logger.Infof("2013年CVE统计信息已更新 - 总数: %d", total)

		// 记录著名漏洞
		if famousCVEs, ok := stats["famous_cves"].([]string); ok && len(famousCVEs) > 0 {
			logger.Infof("发现2013年著名漏洞: %v", famousCVEs)
		}
	}
}

// update2014CVEStats 更新2014年CVE统计信息
func (e *CVEScanningEngine) update2014CVEStats(stats map[string]interface{}) {
	e.mutex.Lock()
	defer e.mutex.Unlock()

	if total, ok := stats["total"].(int); ok && total > 0 {
		// 更新年度统计
		if e.stats.YearlyStats[2014] == nil {
			e.stats.YearlyStats[2014] = &YearlyStats{Year: 2014}
		}
		e.stats.YearlyStats[2014].CVEsScanned = int64(total)

		// 更新严重程度统计
		if severityStats, ok := stats["by_severity"].(map[string]int); ok {
			for severity, count := range severityStats {
				e.stats.SeverityStats[severity] += int64(count)
			}
		}

		// 更新最后更新时间
		e.stats.LastUpdate = time.Now()

		logger.Infof("2014年CVE统计信息已更新 - 总数: %d", total)

		// 记录著名漏洞
		if famousCVEs, ok := stats["famous_cves"].([]string); ok && len(famousCVEs) > 0 {
			logger.Infof("发现2014年著名漏洞: %v", famousCVEs)
		}
	}
}

// update2015CVEStats 更新2015年CVE统计信息
func (e *CVEScanningEngine) update2015CVEStats(stats map[string]interface{}) {
	e.mutex.Lock()
	defer e.mutex.Unlock()

	if total, ok := stats["total"].(int); ok && total > 0 {
		// 更新年度统计
		if e.stats.YearlyStats[2015] == nil {
			e.stats.YearlyStats[2015] = &YearlyStats{Year: 2015}
		}
		e.stats.YearlyStats[2015].CVEsScanned = int64(total)

		// 更新严重程度统计
		if severityStats, ok := stats["by_severity"].(map[string]int); ok {
			for severity, count := range severityStats {
				e.stats.SeverityStats[severity] += int64(count)
			}
		}

		// 更新最后更新时间
		e.stats.LastUpdate = time.Now()

		logger.Infof("2015年CVE统计信息已更新 - 总数: %d", total)

		// 记录著名漏洞
		if famousCVEs, ok := stats["famous_cves"].([]string); ok && len(famousCVEs) > 0 {
			logger.Infof("发现2015年著名漏洞: %v", famousCVEs)
		}
	}
}

// update2016CVEStats 更新2016年CVE统计信息
func (e *CVEScanningEngine) update2016CVEStats(stats map[string]interface{}) {
	e.mutex.Lock()
	defer e.mutex.Unlock()

	if total, ok := stats["total"].(int); ok && total > 0 {
		// 更新年度统计
		if e.stats.YearlyStats[2016] == nil {
			e.stats.YearlyStats[2016] = &YearlyStats{Year: 2016}
		}
		e.stats.YearlyStats[2016].CVEsScanned = int64(total)

		// 更新严重程度统计
		if severityStats, ok := stats["by_severity"].(map[string]int); ok {
			for severity, count := range severityStats {
				e.stats.SeverityStats[severity] += int64(count)
			}
		}

		// 更新最后更新时间
		e.stats.LastUpdate = time.Now()

		logger.Infof("2016年CVE统计信息已更新 - 总数: %d", total)

		// 记录著名漏洞
		if famousCVEs, ok := stats["famous_cves"].([]string); ok && len(famousCVEs) > 0 {
			logger.Infof("发现2016年著名漏洞: %v", famousCVEs)
		}
	}
}

// update2017CVEStats 更新2017年CVE统计信息
func (e *CVEScanningEngine) update2017CVEStats(stats map[string]interface{}) {
	e.mutex.Lock()
	defer e.mutex.Unlock()

	if total, ok := stats["total"].(int); ok && total > 0 {
		// 更新年度统计
		if e.stats.YearlyStats[2017] == nil {
			e.stats.YearlyStats[2017] = &YearlyStats{Year: 2017}
		}
		e.stats.YearlyStats[2017].CVEsScanned = int64(total)

		// 更新严重程度统计
		if severityStats, ok := stats["by_severity"].(map[string]int); ok {
			for severity, count := range severityStats {
				e.stats.SeverityStats[severity] += int64(count)
			}
		}

		// 更新最后更新时间
		e.stats.LastUpdate = time.Now()

		logger.Infof("2017年CVE统计信息已更新 - 总数: %d", total)

		// 记录著名漏洞
		if famousCVEs, ok := stats["famous_cves"].([]string); ok && len(famousCVEs) > 0 {
			logger.Infof("发现2017年著名漏洞: %v", famousCVEs)
		}
	}
}

// update2018CVEStats 更新2018年CVE统计信息
func (e *CVEScanningEngine) update2018CVEStats(stats map[string]interface{}) {
	e.mutex.Lock()
	defer e.mutex.Unlock()

	if total, ok := stats["total"].(int); ok && total > 0 {
		// 更新年度统计
		if e.stats.YearlyStats[2018] == nil {
			e.stats.YearlyStats[2018] = &YearlyStats{Year: 2018}
		}
		e.stats.YearlyStats[2018].CVEsScanned = int64(total)

		// 更新严重程度统计
		if severityStats, ok := stats["by_severity"].(map[string]int); ok {
			for severity, count := range severityStats {
				e.stats.SeverityStats[severity] += int64(count)
			}
		}

		// 更新最后更新时间
		e.stats.LastUpdate = time.Now()

		logger.Infof("2018年CVE统计信息已更新 - 总数: %d", total)

		// 记录著名漏洞
		if famousCVEs, ok := stats["famous_cves"].([]string); ok && len(famousCVEs) > 0 {
			logger.Infof("发现2018年著名漏洞: %v", famousCVEs)
		}
	}
}

// update2019CVEStats 更新2019年CVE统计信息
func (e *CVEScanningEngine) update2019CVEStats(stats map[string]interface{}) {
	e.mutex.Lock()
	defer e.mutex.Unlock()

	if total, ok := stats["total"].(int); ok && total > 0 {
		// 更新年度统计
		if e.stats.YearlyStats[2019] == nil {
			e.stats.YearlyStats[2019] = &YearlyStats{Year: 2019}
		}
		e.stats.YearlyStats[2019].CVEsScanned = int64(total)

		// 更新严重程度统计
		if severityStats, ok := stats["by_severity"].(map[string]int); ok {
			for severity, count := range severityStats {
				e.stats.SeverityStats[severity] += int64(count)
			}
		}

		// 更新最后更新时间
		e.stats.LastUpdate = time.Now()

		logger.Infof("2019年CVE统计信息已更新 - 总数: %d", total)

		// 记录著名漏洞
		if famousCVEs, ok := stats["famous_cves"].([]string); ok && len(famousCVEs) > 0 {
			logger.Infof("发现2019年著名漏洞: %v", famousCVEs)
		}
	}
}

// update2020CVEStats 更新2020年CVE统计信息
func (e *CVEScanningEngine) update2020CVEStats(stats map[string]interface{}) {
	e.mutex.Lock()
	defer e.mutex.Unlock()

	if total, ok := stats["total"].(int); ok && total > 0 {
		// 更新年度统计
		if e.stats.YearlyStats[2020] == nil {
			e.stats.YearlyStats[2020] = &YearlyStats{Year: 2020}
		}
		e.stats.YearlyStats[2020].CVEsScanned = int64(total)

		// 更新严重程度统计
		if severityStats, ok := stats["by_severity"].(map[string]int); ok {
			for severity, count := range severityStats {
				e.stats.SeverityStats[severity] += int64(count)
			}
		}

		// 更新最后更新时间
		e.stats.LastUpdate = time.Now()

		logger.Infof("2020年CVE统计信息已更新 - 总数: %d", total)

		// 记录著名漏洞
		if famousCVEs, ok := stats["famous_cves"].([]string); ok && len(famousCVEs) > 0 {
			logger.Infof("发现2020年著名漏洞: %v", famousCVEs)
		}
	}
}

// update2021CVEStats 更新2021年CVE统计信息
func (e *CVEScanningEngine) update2021CVEStats(stats map[string]interface{}) {
	e.mutex.Lock()
	defer e.mutex.Unlock()

	if total, ok := stats["total"].(int); ok && total > 0 {
		// 更新年度统计
		if e.stats.YearlyStats[2021] == nil {
			e.stats.YearlyStats[2021] = &YearlyStats{Year: 2021}
		}
		e.stats.YearlyStats[2021].CVEsScanned = int64(total)

		// 更新严重程度统计
		if severityStats, ok := stats["by_severity"].(map[string]int); ok {
			for severity, count := range severityStats {
				e.stats.SeverityStats[severity] += int64(count)
			}
		}

		// 更新最后更新时间
		e.stats.LastUpdate = time.Now()

		logger.Infof("2021年CVE统计信息已更新 - 总数: %d", total)

		// 记录著名漏洞
		if famousCVEs, ok := stats["famous_cves"].([]string); ok && len(famousCVEs) > 0 {
			logger.Infof("发现2021年著名漏洞: %v", famousCVEs)
		}
	}
}

// update2022CVEStats 更新2022年CVE统计信息
func (e *CVEScanningEngine) update2022CVEStats(stats map[string]interface{}) {
	e.mutex.Lock()
	defer e.mutex.Unlock()

	if total, ok := stats["total"].(int); ok && total > 0 {
		// 更新年度统计
		if e.stats.YearlyStats[2022] == nil {
			e.stats.YearlyStats[2022] = &YearlyStats{Year: 2022}
		}
		e.stats.YearlyStats[2022].CVEsScanned = int64(total)

		// 更新严重程度统计
		if severityStats, ok := stats["by_severity"].(map[string]int); ok {
			for severity, count := range severityStats {
				e.stats.SeverityStats[severity] += int64(count)
			}
		}

		// 更新最后更新时间
		e.stats.LastUpdate = time.Now()

		logger.Infof("2022年CVE统计信息已更新 - 总数: %d", total)
	}
}

// update2023CVEStats 更新2023年CVE统计信息
func (e *CVEScanningEngine) update2023CVEStats(stats map[string]interface{}) {
	e.mutex.Lock()
	defer e.mutex.Unlock()

	if total, ok := stats["total"].(int); ok && total > 0 {
		// 更新年度统计
		if e.stats.YearlyStats[2023] == nil {
			e.stats.YearlyStats[2023] = &YearlyStats{Year: 2023}
		}
		e.stats.YearlyStats[2023].CVEsScanned = int64(total)

		// 更新严重程度统计
		if severityStats, ok := stats["by_severity"].(map[string]int); ok {
			for severity, count := range severityStats {
				e.stats.SeverityStats[severity] += int64(count)
			}
		}

		// 更新最后更新时间
		e.stats.LastUpdate = time.Now()

		logger.Infof("2023年CVE统计信息已更新 - 总数: %d", total)
	}
}

// update2024CVEStats 更新2024年CVE统计信息
func (e *CVEScanningEngine) update2024CVEStats(stats map[string]interface{}) {
	e.mutex.Lock()
	defer e.mutex.Unlock()

	if total, ok := stats["total"].(int); ok && total > 0 {
		// 更新年度统计
		if e.stats.YearlyStats[2024] == nil {
			e.stats.YearlyStats[2024] = &YearlyStats{Year: 2024}
		}
		e.stats.YearlyStats[2024].CVEsScanned = int64(total)

		// 更新严重程度统计
		if severityStats, ok := stats["by_severity"].(map[string]int); ok {
			for severity, count := range severityStats {
				e.stats.SeverityStats[severity] += int64(count)
			}
		}

		// 更新最后更新时间
		e.stats.LastUpdate = time.Now()

		logger.Infof("2024年CVE统计信息已更新 - 总数: %d", total)
	}
}

// optimizeCVEScanning 优化CVE扫描性能
func (e *CVEScanningEngine) optimizeCVEScanning() {
	logger.Info("开始优化CVE扫描引擎性能...")

	// 1. 启用并发扫描
	e.enableConcurrentScanning()

	// 2. 优化内存使用
	e.optimizeMemoryUsage()

	// 3. 启用智能缓存
	e.enableIntelligentCaching()

	// 4. 优化网络请求
	e.optimizeNetworkRequests()

	logger.Info("CVE扫描引擎性能优化完成")
}

// enableConcurrentScanning 启用并发扫描
func (e *CVEScanningEngine) enableConcurrentScanning() {
	// 设置合理的并发数量
	e.config.MaxConcurrentScans = 10
	e.config.ScanTimeout = 30 * time.Second
	e.config.RequestTimeout = 10 * time.Second

	logger.Infof("并发扫描已启用 - 最大并发数: %d", e.config.MaxConcurrentScans)
}

// optimizeMemoryUsage 优化内存使用
func (e *CVEScanningEngine) optimizeMemoryUsage() {
	// 启用结果缓存限制
	e.config.MaxCacheSize = 1000
	e.config.CacheExpiration = 1 * time.Hour

	// 启用垃圾回收优化
	e.config.EnableGCOptimization = true

	logger.Info("内存使用优化已启用")
}

// enableIntelligentCaching 启用智能缓存
func (e *CVEScanningEngine) enableIntelligentCaching() {
	// 缓存策略配置
	e.config.CacheStrategy = "lru"   // 最近最少使用
	e.config.CacheHitThreshold = 0.8 // 缓存命中率阈值

	logger.Info("智能缓存已启用")
}

// optimizeNetworkRequests 优化网络请求
func (e *CVEScanningEngine) optimizeNetworkRequests() {
	// 连接池配置
	e.config.MaxIdleConns = 100
	e.config.MaxConnsPerHost = 10
	e.config.IdleConnTimeout = 90 * time.Second

	// 重试策略
	e.config.MaxRetries = 3
	e.config.RetryDelay = 1 * time.Second

	logger.Info("网络请求优化已启用")
}

// 扫描实现方法

// performFingerprintIdentification 执行指纹识别
func (e *CVEScanningEngine) performFingerprintIdentification(ctx context.Context, target *types.ScanTarget) ([]*fingerprint.FingerprintResult, error) {
	if e.fingerprintEngine == nil {
		return nil, fmt.Errorf("指纹识别引擎未初始化")
	}

	// 执行指纹识别
	technologies, err := e.fingerprintEngine.IdentifyTechnologies(ctx, target.Value)
	if err != nil {
		return nil, fmt.Errorf("指纹识别失败: %v", err)
	}

	logger.Infof("指纹识别完成 - 识别到 %d 个技术", len(technologies))
	return technologies, nil
}

// performCVEMatching 执行CVE匹配
func (e *CVEScanningEngine) performCVEMatching(ctx context.Context, technologies []*fingerprint.FingerprintResult, config *types.ScanConfig, progress chan<- *types.ScanProgress) ([]*types.Vulnerability, error) {
	var vulnerabilities []*types.Vulnerability

	if e.vulnerabilityMatcher == nil {
		return vulnerabilities, fmt.Errorf("漏洞匹配器未初始化")
	}

	totalTechs := len(technologies)
	for i, tech := range technologies {
		select {
		case <-ctx.Done():
			return vulnerabilities, ctx.Err()
		default:
		}

		// 更新进度
		progressValue := 20 + (i*40)/totalTechs
		select {
		case progress <- &types.ScanProgress{
			Stage:     "CVE匹配",
			Progress:  progressValue,
			Message:   fmt.Sprintf("匹配技术: %s %s", tech.Name, tech.Version),
			Timestamp: time.Now(),
		}:
		case <-ctx.Done():
			return vulnerabilities, ctx.Err()
		}

		// 匹配CVE
		techVulns, err := e.matchCVEsForTechnology(tech, config)
		if err != nil {
			logger.Warnf("技术 %s CVE匹配失败: %v", tech.Name, err)
			continue
		}

		vulnerabilities = append(vulnerabilities, techVulns...)
		e.stats.TotalCVEsScanned += int64(len(techVulns))
	}

	logger.Infof("CVE匹配完成 - 匹配到 %d 个漏洞", len(vulnerabilities))
	return vulnerabilities, nil
}

// matchCVEsForTechnology 为技术匹配CVE
func (e *CVEScanningEngine) matchCVEsForTechnology(tech *fingerprint.FingerprintResult, config *types.ScanConfig) ([]*types.Vulnerability, error) {
	var vulnerabilities []*types.Vulnerability

	// 构建CVE查询
	query := &cve.CVEQuery{
		Product: tech.Name,
		Version: tech.Version,
		Limit:   config.CVEConfig.MaxCVEsPerTarget,
	}

	// 查询CVE
	cveResults, err := e.cveManager.QueryCVE(query)
	if err != nil {
		return vulnerabilities, fmt.Errorf("CVE查询失败: %v", err)
	}

	// 转换为漏洞对象
	for _, cveResult := range cveResults {
		// 计算置信度
		confidence := e.calculateConfidence(tech, cveResult)

		if confidence >= config.CVEConfig.ConfidenceThreshold {
			vuln := e.convertCVEToVulnerability(cveResult, tech, confidence)
			vulnerabilities = append(vulnerabilities, vuln)
		}
	}

	return vulnerabilities, nil
}

// calculateConfidence 计算置信度
func (e *CVEScanningEngine) calculateConfidence(tech *fingerprint.FingerprintResult, cveResult *cve.CVEMatchResult) float64 {
	// 基础置信度来自指纹识别
	baseConfidence := tech.Confidence

	// CVE匹配置信度
	cveConfidence := cveResult.Confidence

	// 综合置信度计算
	combinedConfidence := (baseConfidence + cveConfidence) / 2.0

	// 版本匹配加权
	if e.isVersionMatch(tech.Version, cveResult.CVE.AffectedProducts) {
		combinedConfidence += 0.2
	}

	// 确保置信度在0-1范围内
	if combinedConfidence > 1.0 {
		combinedConfidence = 1.0
	}

	return combinedConfidence
}

// isVersionMatch 检查版本是否匹配
func (e *CVEScanningEngine) isVersionMatch(version string, affectedProducts []*cve.AffectedProduct) bool {
	for _, product := range affectedProducts {
		if e.compareVersions(version, product.StartVersion, product.EndVersion) {
			return true
		}
	}
	return false
}

// compareVersions 比较版本
func (e *CVEScanningEngine) compareVersions(version, startVersion, endVersion string) bool {
	// 简化的版本比较实现
	// 在实际实现中，这里会使用更复杂的版本比较算法
	return version >= startVersion && version <= endVersion
}

// convertCVEToVulnerability 将CVE转换为漏洞对象
func (e *CVEScanningEngine) convertCVEToVulnerability(cveResult *cve.CVEMatchResult, tech *fingerprint.FingerprintResult, confidence float64) *types.Vulnerability {
	cveEntry := cveResult.CVE

	// 确定严重程度
	severity := "Medium"
	cvssScore := 5.0
	if cveEntry.CVSS != nil {
		cvssScore = cveEntry.CVSS.BaseScore
		severity = e.mapCVSSToSeverity(cvssScore)
	}

	return &types.Vulnerability{
		ID:          fmt.Sprintf("cve_%s_%d", cveEntry.ID, time.Now().Unix()),
		Type:        "CVE漏洞",
		Name:        fmt.Sprintf("%s - %s %s", cveEntry.ID, tech.Name, tech.Version),
		Description: cveEntry.Description,
		Severity:    severity,
		CVSS:        cvssScore,
		CVE:         cveEntry.ID,
		URL:         "", // 将在后续步骤中设置
		Method:      "",
		Parameter:   "",
		Payload:     "",
		Evidence:    fmt.Sprintf("技术识别: %s %s (置信度: %.2f)", tech.Name, tech.Version, confidence),
		Solution:    e.generateSolution(cveEntry),
		References:  cveEntry.References,
		Metadata: map[string]interface{}{
			"cve_id":         cveEntry.ID,
			"technology":     tech.Name,
			"version":        tech.Version,
			"confidence":     confidence,
			"published_date": cveEntry.PublishedDate,
			"modified_date":  cveEntry.ModifiedDate,
			"cwe":            cveEntry.CWE,
		},
		CreatedAt: time.Now(),
	}
}

// mapCVSSToSeverity 将CVSS评分映射到严重程度
func (e *CVEScanningEngine) mapCVSSToSeverity(cvssScore float64) string {
	switch {
	case cvssScore >= 9.0:
		return "Critical"
	case cvssScore >= 7.0:
		return "High"
	case cvssScore >= 4.0:
		return "Medium"
	case cvssScore >= 0.1:
		return "Low"
	default:
		return "Info"
	}
}

// generateSolution 生成解决方案
func (e *CVEScanningEngine) generateSolution(cveEntry *cve.CVEEntry) string {
	// 基于CVE信息生成解决方案
	solution := fmt.Sprintf("请及时更新 %s 到最新版本以修复 %s 漏洞。",
		"相关软件", cveEntry.ID)

	// 可以根据CVE类型提供更具体的建议
	if strings.Contains(strings.ToLower(cveEntry.Description), "remote code execution") {
		solution += " 该漏洞可能导致远程代码执行，建议立即修复。"
	}

	return solution
}

// performHistoricalCVEScan 执行历史CVE扫描
func (e *CVEScanningEngine) performHistoricalCVEScan(ctx context.Context, target *types.ScanTarget, technologies []*fingerprint.FingerprintResult, config *types.ScanConfig, progress chan<- *types.ScanProgress) ([]*types.Vulnerability, error) {
	var vulnerabilities []*types.Vulnerability

	if e.historicalScanner == nil {
		return vulnerabilities, fmt.Errorf("历史扫描器未初始化")
	}

	// 按年份扫描
	totalYears := config.CVEConfig.EndYear - config.CVEConfig.StartYear + 1
	for year := config.CVEConfig.StartYear; year <= config.CVEConfig.EndYear; year++ {
		select {
		case <-ctx.Done():
			return vulnerabilities, ctx.Err()
		default:
		}

		// 更新进度
		yearProgress := 60 + ((year-config.CVEConfig.StartYear)*20)/totalYears
		select {
		case progress <- &types.ScanProgress{
			Stage:     "历史CVE扫描",
			Progress:  yearProgress,
			Message:   fmt.Sprintf("扫描 %d 年CVE", year),
			Timestamp: time.Now(),
		}:
		case <-ctx.Done():
			return vulnerabilities, ctx.Err()
		}

		// 扫描该年份的CVE
		yearVulns, err := e.scanCVEsByYear(year, technologies, config)
		if err != nil {
			logger.Warnf("扫描 %d 年CVE失败: %v", year, err)
			continue
		}

		vulnerabilities = append(vulnerabilities, yearVulns...)

		// 更新年度统计
		if e.stats.YearlyStats[year] != nil {
			e.stats.YearlyStats[year].VulnsFound += int64(len(yearVulns))
		}
	}

	logger.Infof("历史CVE扫描完成 - 发现 %d 个历史漏洞", len(vulnerabilities))
	return vulnerabilities, nil
}

// scanCVEsByYear 按年份扫描CVE
func (e *CVEScanningEngine) scanCVEsByYear(year int, technologies []*fingerprint.FingerprintResult, config *types.ScanConfig) ([]*types.Vulnerability, error) {
	var vulnerabilities []*types.Vulnerability

	// 为每个技术查询该年份的CVE
	for _, tech := range technologies {
		query := &cve.CVEQuery{
			Product: tech.Name,
			Version: tech.Version,
			Limit:   100, // 每年最多100个CVE
		}

		cveResults, err := e.cveManager.QueryCVE(query)
		if err != nil {
			logger.Warnf("查询 %d 年 %s CVE失败: %v", year, tech.Name, err)
			continue
		}

		// 转换为漏洞对象
		for _, cveResult := range cveResults {
			confidence := e.calculateConfidence(tech, cveResult)
			if confidence >= config.CVEConfig.ConfidenceThreshold {
				vuln := e.convertCVEToVulnerability(cveResult, tech, confidence)
				// 添加年份标记
				vuln.Metadata["scan_year"] = year
				vulnerabilities = append(vulnerabilities, vuln)
			}
		}
	}

	return vulnerabilities, nil
}

// performPoCVerification 执行PoC验证
func (e *CVEScanningEngine) performPoCVerification(ctx context.Context, target *types.ScanTarget, vulnerabilities []*types.Vulnerability, progress chan<- *types.ScanProgress) []*types.Vulnerability {
	if e.pocExecutor == nil {
		logger.Warnf("PoC执行器未初始化，跳过PoC验证")
		return vulnerabilities
	}

	var verifiedVulns []*types.Vulnerability
	totalVulns := len(vulnerabilities)

	for i, vuln := range vulnerabilities {
		select {
		case <-ctx.Done():
			return verifiedVulns
		default:
		}

		// 更新进度
		pocProgress := 80 + (i*10)/totalVulns
		select {
		case progress <- &types.ScanProgress{
			Stage:     "PoC验证",
			Progress:  pocProgress,
			Message:   fmt.Sprintf("验证漏洞: %s", vuln.CVE),
			Timestamp: time.Now(),
		}:
		case <-ctx.Done():
			return verifiedVulns
		}

		// 执行PoC验证
		verified, pocResult := e.verifyVulnerabilityWithPoC(ctx, target, vuln)
		if verified {
			// 更新漏洞信息
			vuln.Metadata["poc_verified"] = true
			vuln.Metadata["poc_result"] = pocResult
			vuln.Evidence += fmt.Sprintf(" | PoC验证成功: %s", pocResult)
			e.stats.TotalPoCsExecuted++
		} else {
			vuln.Metadata["poc_verified"] = false
		}

		verifiedVulns = append(verifiedVulns, vuln)
	}

	logger.Infof("PoC验证完成 - 验证了 %d 个漏洞", len(verifiedVulns))
	return verifiedVulns
}

// verifyVulnerabilityWithPoC 使用PoC验证漏洞
func (e *CVEScanningEngine) verifyVulnerabilityWithPoC(ctx context.Context, target *types.ScanTarget, vuln *types.Vulnerability) (bool, string) {
	// 查找对应的PoC
	poc := e.findPoCForCVE(vuln.CVE)
	if poc == nil {
		return false, "未找到对应的PoC"
	}

	// 执行PoC
	result, err := e.executePoCWithSafety(ctx, target, poc)
	if err != nil {
		return false, fmt.Sprintf("PoC执行失败: %v", err)
	}

	return result.Success, result.Message
}

// findPoCForCVE 查找CVE对应的PoC
func (e *CVEScanningEngine) findPoCForCVE(cveID string) *CVEPoCEntry {
	// 简化实现：返回模拟PoC
	return &CVEPoCEntry{
		ID:          fmt.Sprintf("poc_%s", cveID),
		CVEID:       cveID,
		Name:        fmt.Sprintf("PoC for %s", cveID),
		Description: "概念验证代码",
		Type:        "http",
		Code:        "GET / HTTP/1.1\nHost: target\n\n",
		Safety:      "safe",
		Author:      "Security Researcher",
		Source:      "Public",
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}
}

// executePoCWithSafety 安全执行PoC
func (e *CVEScanningEngine) executePoCWithSafety(ctx context.Context, target *types.ScanTarget, poc *CVEPoCEntry) (*PoCExecutionResult, error) {
	// 安全检查
	if poc.Safety == "dangerous" {
		return &PoCExecutionResult{
			Success: false,
			Message: "PoC被标记为危险，跳过执行",
		}, nil
	}

	// 简化的PoC执行
	// 在实际实现中，这里会根据PoC类型执行相应的验证逻辑
	return &PoCExecutionResult{
		Success: false, // 默认为失败，避免误报
		Message: "PoC执行完成（模拟）",
	}, nil
}

// PoCExecutionResult PoC执行结果
type PoCExecutionResult struct {
	Success   bool      `json:"success"`
	Message   string    `json:"message"`
	Evidence  string    `json:"evidence"`
	Timestamp time.Time `json:"timestamp"`
}

// performExploitDetection 执行利用检测
func (e *CVEScanningEngine) performExploitDetection(ctx context.Context, vulnerabilities []*types.Vulnerability, progress chan<- *types.ScanProgress) []*types.Vulnerability {
	if e.exploitDetector == nil {
		logger.Warnf("利用检测器未初始化，跳过利用检测")
		return vulnerabilities
	}

	var enhancedVulns []*types.Vulnerability
	totalVulns := len(vulnerabilities)

	for i, vuln := range vulnerabilities {
		select {
		case <-ctx.Done():
			return enhancedVulns
		default:
		}

		// 更新进度
		exploitProgress := 90 + (i*5)/totalVulns
		select {
		case progress <- &types.ScanProgress{
			Stage:     "利用检测",
			Progress:  exploitProgress,
			Message:   fmt.Sprintf("检测利用: %s", vuln.CVE),
			Timestamp: time.Now(),
		}:
		case <-ctx.Done():
			return enhancedVulns
		}

		// 检测公开利用
		exploits := e.detectExploitsForCVE(vuln.CVE)
		if len(exploits) > 0 {
			vuln.Metadata["public_exploits"] = exploits
			vuln.Metadata["exploit_count"] = len(exploits)
			vuln.Evidence += fmt.Sprintf(" | 发现 %d 个公开利用", len(exploits))
			e.stats.TotalExploitsFound++
		}

		enhancedVulns = append(enhancedVulns, vuln)
	}

	logger.Infof("利用检测完成 - 检测了 %d 个漏洞", len(enhancedVulns))
	return enhancedVulns
}

// detectExploitsForCVE 检测CVE的公开利用
func (e *CVEScanningEngine) detectExploitsForCVE(cveID string) []*ExploitEntry {
	var exploits []*ExploitEntry

	// 简化实现：模拟检测到的利用
	// 在实际实现中，这里会查询各种利用数据库
	if strings.Contains(cveID, "2021") || strings.Contains(cveID, "2022") {
		exploits = append(exploits, &ExploitEntry{
			ID:          fmt.Sprintf("exploit_%s_1", cveID),
			CVEID:       cveID,
			Name:        fmt.Sprintf("Exploit for %s", cveID),
			Description: "公开利用代码",
			Type:        "exploit-db",
			Platform:    []string{"linux", "windows"},
			Reliability: "good",
			Source:      "exploit-db",
			URL:         fmt.Sprintf("https://www.exploit-db.com/exploits/%s", cveID),
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		})
	}

	return exploits
}

// 辅助方法

// deduplicateAndSortVulnerabilities 去重和排序漏洞
func (e *CVEScanningEngine) deduplicateAndSortVulnerabilities(vulnerabilities []*types.Vulnerability) []*types.Vulnerability {
	// 使用map去重
	vulnMap := make(map[string]*types.Vulnerability)

	for _, vuln := range vulnerabilities {
		key := fmt.Sprintf("%s_%s", vuln.CVE, vuln.URL)
		if existing, exists := vulnMap[key]; exists {
			// 保留置信度更高的漏洞
			if existingConf, ok := existing.Metadata["confidence"].(float64); ok {
				if newConf, ok := vuln.Metadata["confidence"].(float64); ok && newConf > existingConf {
					vulnMap[key] = vuln
				}
			}
		} else {
			vulnMap[key] = vuln
		}
	}

	// 转换为切片
	var deduplicatedVulns []*types.Vulnerability
	for _, vuln := range vulnMap {
		deduplicatedVulns = append(deduplicatedVulns, vuln)
	}

	// 按CVSS评分排序
	sort.Slice(deduplicatedVulns, func(i, j int) bool {
		return deduplicatedVulns[i].CVSS > deduplicatedVulns[j].CVSS
	})

	return deduplicatedVulns
}

// applyFilters 应用过滤器
func (e *CVEScanningEngine) applyFilters(vulnerabilities []*types.Vulnerability, config *types.ScanConfig) []*types.Vulnerability {
	var filteredVulns []*types.Vulnerability

	for _, vuln := range vulnerabilities {
		// 严重程度过滤
		if e.shouldIncludeSeverity(vuln.Severity, config.CVEConfig.SeverityFilter) {
			filteredVulns = append(filteredVulns, vuln)
		}
	}

	// 限制数量
	if len(filteredVulns) > config.CVEConfig.MaxCVEsPerTarget {
		filteredVulns = filteredVulns[:config.CVEConfig.MaxCVEsPerTarget]
	}

	return filteredVulns
}

// shouldIncludeSeverity 检查是否应包含该严重程度
func (e *CVEScanningEngine) shouldIncludeSeverity(severity string, severityFilter []string) bool {
	if len(severityFilter) == 0 {
		return true // 无过滤器，包含所有
	}

	for _, filter := range severityFilter {
		if strings.EqualFold(severity, filter) {
			return true
		}
	}

	return false
}

// updateScanStatistics 更新扫描统计
func (e *CVEScanningEngine) updateScanStatistics(result *types.ScanResult) {
	e.mutex.Lock()
	defer e.mutex.Unlock()

	e.stats.TotalScans++
	e.stats.TotalVulnsFound += int64(len(result.Vulnerabilities))

	// 更新严重程度统计
	for _, vuln := range result.Vulnerabilities {
		e.stats.SeverityStats[vuln.Severity]++
	}

	e.stats.LastUpdate = time.Now()
}

// addScanMetadata 添加扫描元数据
func (e *CVEScanningEngine) addScanMetadata(result *types.ScanResult, config *types.ScanConfig) {
	result.Metadata["cve_engine_version"] = "1.0.0"
	result.Metadata["scan_strategy"] = config.CVEConfig.ScanStrategy
	result.Metadata["historical_scan_enabled"] = config.CVEConfig.EnableHistoricalScan
	result.Metadata["poc_verification_enabled"] = config.CVEConfig.EnablePoCVerification
	result.Metadata["exploit_detection_enabled"] = config.CVEConfig.EnableExploitDetection
	result.Metadata["scan_year_range"] = fmt.Sprintf("%d-%d", config.CVEConfig.StartYear, config.CVEConfig.EndYear)
	result.Metadata["confidence_threshold"] = config.CVEConfig.ConfidenceThreshold
	result.Metadata["total_cves_scanned"] = e.stats.TotalCVEsScanned
	result.Metadata["total_pocs_executed"] = e.stats.TotalPoCsExecuted
	result.Metadata["total_exploits_found"] = e.stats.TotalExploitsFound
}
