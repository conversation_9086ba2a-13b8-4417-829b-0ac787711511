#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试API响应数据结构
"""

import requests
import json

def test_api_response():
    """测试target-info API的响应数据"""
    print("🔍 测试target-info API响应数据")
    print("=" * 60)
    
    # 登录获取token
    login_data = {"username": "admin", "password": "admin123"}
    response = requests.post("http://localhost:8082/api/v1/auth/login", json=login_data, timeout=5)
    
    if response.status_code != 200:
        print("❌ 登录失败")
        return
    
    token = response.json().get('data', {}).get('token')
    headers = {"Authorization": f"Bearer {token}"}
    
    print("✅ 登录成功")
    
    # 测试任务327的target-info API
    task_id = 327
    print(f"\n🔍 测试任务{task_id}的target-info API:")
    
    try:
        response = requests.get(f"http://localhost:8082/api/v1/scans/{task_id}/target-info",
                              headers=headers, timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            
            print(f"✅ API调用成功")
            print(f"📋 响应状态码: {response.status_code}")
            print(f"📋 响应数据结构:")
            
            # 美化打印JSON数据
            print(json.dumps(data, indent=2, ensure_ascii=False))
            
            # 重点检查tech_stack部分
            target_data = data.get('data', {})
            targets = target_data.get('targets', [])
            
            if targets:
                print(f"\n🔧 技术栈信息详细分析:")
                for i, target in enumerate(targets, 1):
                    print(f"  目标 {i}:")
                    tech_stack = target.get('tech_stack', {})
                    
                    print(f"    Web服务器: {tech_stack.get('web_server', '未检测到')}")
                    print(f"    编程语言: {tech_stack.get('language', '未检测到')}")
                    print(f"    开发框架: {tech_stack.get('framework', '未检测到')}")
                    print(f"    数据库: {tech_stack.get('database', '未检测到')}")
                    print(f"    CMS系统: {tech_stack.get('cms', '未检测到')}")
                    print(f"    技术组件: {tech_stack.get('libraries', [])}")
                    
                    # 检查是否有language字段
                    if tech_stack.get('language'):
                        print(f"    ✅ 编程语言字段存在且有值: {tech_stack.get('language')}")
                    else:
                        print(f"    ❌ 编程语言字段为空或不存在")
                        
                    # 检查原始tech_stack数据
                    print(f"    📊 原始tech_stack数据: {tech_stack}")
            else:
                print(f"  ❌ 没有找到目标数据")
                
        else:
            print(f"❌ API调用失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except Exception as e:
        print(f"❌ API调用异常: {e}")

if __name__ == "__main__":
    test_api_response()
