#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试技术栈信息显示功能
"""

import requests
import json
import time

def test_tech_stack_display():
    """测试技术栈信息显示"""
    print("🎯 测试技术栈信息显示功能")
    print("=" * 60)
    
    # 登录获取token
    login_data = {"username": "admin", "password": "admin123"}
    response = requests.post("http://localhost:8082/api/v1/auth/login", json=login_data, timeout=5)
    
    if response.status_code != 200:
        print("❌ 登录失败")
        return
    
    token = response.json().get('data', {}).get('token')
    headers = {"Authorization": f"Bearer {token}"}
    
    print("✅ 登录成功")
    
    # 创建一个Web扫描任务，目标是PHP网站
    scan_data = {
        "name": f"技术栈测试-{int(time.time())}",
        "type": "web",
        "targets": ["http://testphp.vulnweb.com"],
        "config": {
            "scan_type": "web",
            "timeout": 60
        }
    }
    
    # 创建扫描任务
    response = requests.post("http://localhost:8082/api/v1/scans", 
                           json=scan_data, headers=headers, timeout=10)
    
    if response.status_code != 200:
        print(f"❌ 创建扫描任务失败: {response.status_code}")
        print(f"响应内容: {response.text}")
        return
    
    task_data = response.json().get('data', {})
    task_id = task_data.get('id')
    print(f"✅ 创建扫描任务成功，任务ID: {task_id}")
    
    # 等待扫描完成
    print("\n📊 等待扫描完成...")
    max_wait_time = 120  # 最大等待2分钟
    start_time = time.time()
    
    while time.time() - start_time < max_wait_time:
        try:
            response = requests.get(f"http://localhost:8082/api/v1/scans/{task_id}",
                                  headers=headers, timeout=5)
            
            if response.status_code == 200:
                task_data = response.json().get('data', {})
                status = task_data.get('status', 'unknown')
                progress = task_data.get('progress', 0)
                
                print(f"  状态: {status}, 进度: {progress}%")
                
                if status in ['completed', 'failed', 'cancelled']:
                    print(f"✅ 扫描完成，最终状态: {status}")
                    break
                    
            time.sleep(10)  # 等待10秒后再次检查
            
        except Exception as e:
            print(f"❌ 获取扫描状态失败: {e}")
            break
    
    # 检查目标信息
    print(f"\n🔍 检查目标信息:")
    try:
        response = requests.get(f"http://localhost:8082/api/v1/scans/{task_id}/target-info",
                              headers=headers, timeout=5)
        
        if response.status_code == 200:
            target_info = response.json().get('data', {})
            
            print(f"📋 目标信息:")
            print(f"  基础信息: {target_info.get('basic_info', {})}")
            
            # 重点检查技术栈信息
            tech_stack = target_info.get('tech_stack', {})
            print(f"\n🔧 技术栈信息:")
            print(f"  Web服务器: {tech_stack.get('web_server', '未检测到')}")
            print(f"  编程语言: {tech_stack.get('language', '未检测到')}")
            print(f"  框架: {tech_stack.get('framework', '未检测到')}")
            print(f"  数据库: {tech_stack.get('database', '未检测到')}")
            print(f"  CMS: {tech_stack.get('cms', '未检测到')}")
            print(f"  库/组件: {tech_stack.get('libraries', [])}")
            
            # 检查服务信息
            services = target_info.get('services', {})
            print(f"\n🌐 服务信息:")
            print(f"  Web服务: {services.get('web_service', {})}")
            
            # 检查目录结构
            directory_structure = target_info.get('directory_structure', {})
            print(f"\n📁 目录结构:")
            accessible_dirs = directory_structure.get('accessible_directories', [])
            print(f"  可访问目录数: {len(accessible_dirs)}")
            if accessible_dirs:
                print(f"  示例目录:")
                for i, dir_info in enumerate(accessible_dirs[:3], 1):
                    print(f"    {i}. {dir_info.get('path', 'unknown')} (状态码: {dir_info.get('status_code', 'unknown')})")
            
            # 检查统计信息
            statistics = target_info.get('statistics', {})
            print(f"\n📊 统计信息:")
            print(f"  指纹识别数: {statistics.get('fingerprints_found', 0)}")
            print(f"  目录发现数: {statistics.get('directories_found', 0)}")
            print(f"  扫描时间: {statistics.get('scan_time', 'unknown')}")
            
            # 检查是否检测到PHP
            if 'PHP' in str(tech_stack) or 'php' in str(tech_stack).lower():
                print(f"\n✅ 成功检测到PHP技术栈!")
            else:
                print(f"\n⚠️ 未检测到PHP，可能需要检查指纹识别逻辑")
                
        else:
            print(f"❌ 获取目标信息失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 检查目标信息失败: {e}")
    
    # 检查扫描日志中的指纹识别信息
    print(f"\n📋 检查扫描日志:")
    try:
        response = requests.get(f"http://localhost:8082/api/v1/scans/{task_id}/logs",
                              headers=headers, timeout=5)
        
        if response.status_code == 200:
            logs_data = response.json().get('data', [])
            
            # 查找指纹识别相关日志
            fingerprint_logs = [log for log in logs_data if '指纹识别' in log.get('message', '') or 'PHP' in log.get('message', '')]
            
            print(f"  指纹识别相关日志: {len(fingerprint_logs)} 条")
            
            if fingerprint_logs:
                print("  📝 指纹识别日志:")
                for i, log in enumerate(fingerprint_logs[:5], 1):
                    print(f"    {i}. [{log.get('level', 'INFO')}] {log.get('message', 'unknown')}")
            
            # 查找信息收集数据保存日志
            info_save_logs = [log for log in logs_data if '信息收集数据' in log.get('message', '')]
            
            if info_save_logs:
                print(f"\n  📝 信息收集数据保存日志:")
                for i, log in enumerate(info_save_logs, 1):
                    print(f"    {i}. [{log.get('level', 'INFO')}] {log.get('message', 'unknown')}")
            else:
                print(f"\n  ⚠️ 未找到信息收集数据保存日志")
                
        else:
            print(f"❌ 获取扫描日志失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 检查扫描日志失败: {e}")
    
    print("\n" + "=" * 60)
    print("📋 测试总结:")
    print("1. 扫描任务创建: ✅ 成功")
    print("2. 扫描任务完成: ✅ 成功")
    print("3. 目标信息API: 🔍 已测试")
    print("4. 技术栈显示: 🎯 等待验证")
    print("5. 信息收集数据: 📊 等待验证")

if __name__ == "__main__":
    test_tech_stack_display()
