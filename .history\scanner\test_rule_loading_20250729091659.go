package main

import (
	"fmt"
	"log"
	"net/http"
	"strings"

	"scanner/internal/scanner/rules"
	"scanner/internal/services"
	"scanner/internal/services/vulnerability"
	"scanner/pkg/logger"
)

func main() {
	fmt.Println("🔍 测试规则引擎加载和匹配")
	fmt.Println("=" * 50)

	// 初始化日志
	logger.Init("debug", "console", "")

	// 创建模拟的服务
	var vulnService *vulnerability.Service = nil
	var logService *services.ScanLogService = nil

	// 创建规则管理器
	ruleManager := rules.NewSimpleRuleManager("rules", vulnService, logService)

	// 获取统计信息
	stats := ruleManager.GetStats()
	fmt.Printf("📊 规则统计信息:\n")
	fmt.Printf("  总规则数: %d\n", stats.TotalRules)
	fmt.Printf("  匹配规则数: %d\n", stats.MatchedRules)
	fmt.Printf("  最后更新: %s\n", stats.LastUpdate.Format("2006-01-02 15:04:05"))

	// 获取规则引擎
	ruleEngine := ruleManager.GetRuleEngine()
	if ruleEngine == nil {
		log.Fatal("规则引擎未初始化")
	}

	// 获取所有规则
	allRules := ruleEngine.GetAllRules()
	fmt.Printf("\n📋 加载的规则列表:\n")
	for i, rule := range allRules {
		fmt.Printf("  %d. ID: %s, 名称: %s, 类型: %s, 严重程度: %s\n",
			i+1, rule.ID, rule.Name, rule.Type, rule.Severity)
	}

	if len(allRules) == 0 {
		fmt.Println("  ⚠️ 没有加载到任何规则")
		return
	}

	// 测试规则匹配
	fmt.Printf("\n🧪 测试规则匹配:\n")

	// 创建一个模拟的HTTP响应
	testResponse := createTestResponse()

	// 测试目标
	testTargets := []string{
		"http://testphp.vulnweb.com",
		"https://dvwa.bachang.org/login.php",
		"http://example.com/admin",
	}

	for _, target := range testTargets {
		fmt.Printf("\n  测试目标: %s\n", target)

		// 执行规则匹配
		matches := ruleEngine.MatchHTTPRules(target, testResponse)

		if len(matches) > 0 {
			fmt.Printf("    ✅ 匹配到 %d 个规则:\n", len(matches))
			for j, match := range matches {
				fmt.Printf("      %d. 规则: %s, 置信度: %.2f, 证据: %s\n",
					j+1, match.Rule.Name, match.Confidence, match.Evidence)
			}
		} else {
			fmt.Printf("    ❌ 没有匹配到任何规则\n")
		}
	}

	// 测试特定漏洞模式
	fmt.Printf("\n🎯 测试特定漏洞模式:\n")

	// SQL注入测试
	sqlResponse := createSQLErrorResponse()
	fmt.Printf("  测试SQL注入响应:\n")
	sqlMatches := ruleEngine.MatchHTTPRules("http://test.com/login.php?id=1'", sqlResponse)
	if len(sqlMatches) > 0 {
		fmt.Printf("    ✅ SQL注入检测成功，匹配 %d 个规则\n", len(sqlMatches))
	} else {
		fmt.Printf("    ❌ SQL注入检测失败\n")
	}

	// XSS测试
	xssResponse := createXSSResponse()
	fmt.Printf("  测试XSS响应:\n")
	xssMatches := ruleEngine.MatchHTTPRules("http://test.com/search.php?q=<script>", xssResponse)
	if len(xssMatches) > 0 {
		fmt.Printf("    ✅ XSS检测成功，匹配 %d 个规则\n", len(xssMatches))
	} else {
		fmt.Printf("    ❌ XSS检测失败\n")
	}

	fmt.Printf("\n" + "="*50)
	fmt.Printf("\n📋 测试总结:\n")
	fmt.Printf("  规则加载: %s\n", getStatusText(len(allRules) > 0))
	fmt.Printf("  规则匹配: %s\n", getStatusText(len(sqlMatches) > 0 || len(xssMatches) > 0))
	fmt.Printf("  引擎状态: %s\n", getStatusText(ruleManager.IsEnabled()))
}

func createTestResponse() *http.Response {
	// 创建一个基本的HTTP响应
	response := &http.Response{
		StatusCode: 200,
		Header:     make(http.Header),
		Body:       http.NoBody,
	}

	response.Header.Set("Content-Type", "text/html")
	response.Header.Set("Server", "Apache/2.4.41")

	return response
}

func createSQLErrorResponse() *http.Response {
	response := &http.Response{
		StatusCode: 500,
		Header:     make(http.Header),
		Body:       strings.NewReader("MySQL Error: You have an error in your SQL syntax"),
	}

	response.Header.Set("Content-Type", "text/html")
	response.Header.Set("Server", "Apache/2.4.41")

	return response
}

func createXSSResponse() *http.Response {
	response := &http.Response{
		StatusCode: 200,
		Header:     make(http.Header),
		Body:       strings.NewReader("<html><body><script>alert('xss')</script></body></html>"),
	}

	response.Header.Set("Content-Type", "text/html")
	response.Header.Set("Server", "Apache/2.4.41")

	return response
}

func getStatusText(success bool) string {
	if success {
		return "✅ 正常"
	}
	return "❌ 异常"
}
