package database

import (
	"fmt"
	"os"
	"path/filepath"

	"scanner/internal/config"
	"scanner/internal/database"
	"scanner/internal/models"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// DB 数据库实例
var DB *gorm.DB

// Init 初始化数据库连接
func Init(cfg config.DatabaseConfig) (*gorm.DB, error) {
	var err error

	// 确保数据目录存在
	dataDir := filepath.Dir(cfg.DSN)
	if err := os.MkdirAll(dataDir, 0755); err != nil {
		return nil, fmt.Errorf("创建数据目录失败: %w", err)
	}

	// 构建DSN
	dsn := buildDSN(cfg.DSN)

	// 配置GORM
	gormConfig := &gorm.Config{
		Logger: getLogger(cfg.LogLevel),
	}

	// 连接数据库
	DB, err = gorm.Open(sqlite.Open(dsn), gormConfig)
	if err != nil {
		return nil, fmt.Errorf("连接数据库失败: %w", err)
	}

	// 配置连接池
	sqlDB, err := DB.DB()
	if err != nil {
		return nil, fmt.Errorf("获取数据库实例失败: %w", err)
	}

	sqlDB.SetMaxOpenConns(cfg.MaxOpenConn)
	sqlDB.SetMaxIdleConns(cfg.MaxIdleConn)
	sqlDB.SetConnMaxLifetime(cfg.ConnMaxLifetime)

	// SQLite优化
	if err := optimizeSQLite(DB); err != nil {
		return nil, fmt.Errorf("SQLite优化失败: %w", err)
	}

	// 自动迁移
	if cfg.AutoMigrate {
		if err := autoMigrate(DB); err != nil {
			return nil, fmt.Errorf("数据库迁移失败: %w", err)
		}

		// 存储层迁移
		storageMigration := database.NewStorageMigration(DB)
		if err := storageMigration.MigrateStorageTables(); err != nil {
			return nil, fmt.Errorf("存储层迁移失败: %w", err)
		}

		// 初始化存储层基础数据
		if err := storageMigration.InitializeStorageData(); err != nil {
			return nil, fmt.Errorf("存储层数据初始化失败: %w", err)
		}
	}

	return DB, nil
}

func buildDSN(dsn string) string {
	// SQLite优化参数
	return dsn + "?cache=shared&mode=rwc&_journal_mode=WAL&_synchronous=NORMAL&_cache_size=1000&_temp_store=memory"
}

func getLogger(level string) logger.Interface {
	var logLevel logger.LogLevel
	switch level {
	case "silent":
		logLevel = logger.Silent
	case "error":
		logLevel = logger.Error
	case "warn":
		logLevel = logger.Warn
	case "info":
		logLevel = logger.Info
	default:
		logLevel = logger.Warn
	}

	return logger.Default.LogMode(logLevel)
}

func optimizeSQLite(db *gorm.DB) error {
	optimizations := []string{
		"PRAGMA journal_mode = WAL;",
		"PRAGMA synchronous = NORMAL;",
		"PRAGMA cache_size = 1000;",
		"PRAGMA temp_store = memory;",
		"PRAGMA mmap_size = 268435456;",
		"PRAGMA foreign_keys = ON;",
		"PRAGMA auto_vacuum = INCREMENTAL;",
	}

	for _, sql := range optimizations {
		if err := db.Exec(sql).Error; err != nil {
			return err
		}
	}

	return nil
}

func autoMigrate(db *gorm.DB) error {
	return db.AutoMigrate(
		&models.User{},
		&models.Asset{},
		&models.ScanTask{},
		&models.ScanProgress{},
		&models.Vulnerability{},
		// &models.Ticket{}, // 跳过工单表迁移，手动管理
		&models.Report{},
		&models.SystemConfig{},
		&models.OperationLog{},
		&models.ScanLog{},
		&models.InfoGatheringLog{}, // 新增信息收集日志表

		// AI分析模型
		&models.Conversation{},
		&models.ConversationMessage{},
		&models.AIAnalysisResult{},
		&models.AIUsageStats{},
		&models.AIBatchJob{},
		&models.AIKnowledgeBase{},

		// 存储层模型
		&models.VulnerabilityEntry{},
		&models.DetectionRule{},
		&models.RuleGroup{},
		&models.RuleGroupRelation{},
		&models.FingerprintEntry{},
		&models.FingerprintRule{},
		&models.KnowledgeEntry{},
		&models.KnowledgeCategory{},
		&models.KnowledgeRelation{},

		// 合规管理模型
		&models.ComplianceStandard{},
		&models.ComplianceControl{},
		&models.ComplianceAssessment{},
		&models.ComplianceResult{},
		&models.ComplianceAuditLog{},
	)
}

// Close 关闭数据库连接
func Close() error {
	if DB != nil {
		sqlDB, err := DB.DB()
		if err != nil {
			return err
		}
		return sqlDB.Close()
	}
	return nil
}

// GetDB 获取数据库实例
func GetDB() *gorm.DB {
	return DB
}

// Transaction 执行事务
func Transaction(fn func(*gorm.DB) error) error {
	return DB.Transaction(fn)
}
