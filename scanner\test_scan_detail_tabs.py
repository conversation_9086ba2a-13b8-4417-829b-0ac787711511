#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试扫描详情页面标签页结构
验证信息收集标签页是否已被删除
"""

import requests
import json

# 配置
BASE_URL = "http://localhost:8082/api/v1"
FRONTEND_URL = "http://localhost:3000"

# 认证信息
USERNAME = "admin"
PASSWORD = "admin123"

def login():
    """登录获取token"""
    print("🔐 正在登录...")
    
    login_data = {
        "username": USERNAME,
        "password": PASSWORD
    }
    
    try:
        response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
        if response.status_code == 200:
            data = response.json()
            token = data.get('data', {}).get('token')
            if token:
                print("✅ 登录成功")
                return token
            else:
                print("❌ 登录响应中没有token")
                return None
        else:
            print(f"❌ 登录失败: {response.text}")
            return None
    except Exception as e:
        print(f"❌ 登录请求失败: {e}")
        return None

def get_headers(token):
    """获取带认证的请求头"""
    return {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }

def test_scan_list(token):
    """测试扫描任务列表"""
    print("\n📋 测试扫描任务列表...")
    headers = get_headers(token)
    
    try:
        response = requests.get(f"{BASE_URL}/scans", headers=headers)
        if response.status_code == 200:
            data = response.json()
            tasks = data.get('data', {}).get('tasks', [])
            print(f"✅ 获取到 {len(tasks)} 个扫描任务")
            
            if tasks:
                # 返回第一个任务的ID用于测试详情页面
                first_task = tasks[0]
                print(f"📝 第一个任务: ID={first_task.get('id')}, 名称={first_task.get('name')}, 状态={first_task.get('status')}")
                return first_task.get('id')
            else:
                print("ℹ️ 暂无扫描任务")
                return None
        else:
            print(f"❌ 获取任务列表失败: {response.text}")
            return None
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return None

def test_scan_detail(task_id, token):
    """测试扫描任务详情"""
    print(f"\n🔍 测试扫描任务详情 (ID: {task_id})...")
    headers = get_headers(token)
    
    try:
        response = requests.get(f"{BASE_URL}/scans/{task_id}", headers=headers)
        if response.status_code == 200:
            data = response.json()
            task_data = data.get('data', {})
            print(f"✅ 任务详情获取成功")
            print(f"   任务名称: {task_data.get('name')}")
            print(f"   任务类型: {task_data.get('type')}")
            print(f"   任务状态: {task_data.get('status')}")
            print(f"   扫描进度: {task_data.get('progress', 0)}%")
            return True
        else:
            print(f"❌ 获取任务详情失败: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False

def test_scan_logs(task_id, token):
    """测试扫描日志"""
    print(f"\n📝 测试扫描日志 (ID: {task_id})...")
    headers = get_headers(token)
    
    try:
        response = requests.get(f"{BASE_URL}/scan-logs/{task_id}", headers=headers)
        if response.status_code == 200:
            data = response.json()
            logs = data.get('data', [])
            print(f"✅ 获取到 {len(logs)} 条扫描日志")
            
            if logs:
                # 显示最近的几条日志
                for i, log in enumerate(logs[:3]):
                    print(f"   日志 {i+1}: [{log.get('level')}] {log.get('stage')} - {log.get('message')}")
            return True
        else:
            print(f"❌ 获取扫描日志失败: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False

def test_info_gathering_removed():
    """测试信息收集功能是否已被移除"""
    print("\n🗑️ 验证信息收集功能是否已被移除...")
    
    # 检查前端页面是否还包含信息收集相关内容
    try:
        response = requests.get(f"{FRONTEND_URL}/scans")
        if response.status_code == 200:
            content = response.text
            
            # 检查是否还有信息收集相关的文本
            info_gathering_keywords = [
                "信息收集",
                "info-gathering", 
                "InfoGatheringResults",
                "指纹识别标签页",
                "目录发现标签页"
            ]
            
            found_keywords = []
            for keyword in info_gathering_keywords:
                if keyword in content:
                    found_keywords.append(keyword)
            
            if found_keywords:
                print(f"⚠️ 仍然发现信息收集相关内容: {found_keywords}")
                return False
            else:
                print("✅ 信息收集功能已成功移除")
                return True
        else:
            print(f"❌ 无法访问前端页面: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def main():
    """主函数"""
    
    print("🎯 扫描详情页面标签页结构测试")
    print("=" * 50)
    
    # 1. 登录获取token
    token = login()
    if not token:
        print("❌ 无法获取认证token，退出测试")
        return
    
    # 2. 获取扫描任务列表
    task_id = test_scan_list(token)
    
    # 3. 如果有任务，测试任务详情
    if task_id:
        test_scan_detail(task_id, token)
        test_scan_logs(task_id, token)
    
    # 4. 验证信息收集功能是否已被移除
    test_info_gathering_removed()
    
    print("\n✅ 测试完成！")
    print("\n📋 扫描详情页面现在应该只包含以下标签页：")
    print("   1. 扫描结果 - 显示扫描发现的漏洞和目标信息")
    print("   2. 扫描日志 - 显示扫描过程的详细日志")
    print("   ❌ 信息收集 - 已被移除")

if __name__ == "__main__":
    main()
