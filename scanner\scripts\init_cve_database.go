package main

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"path/filepath"
	"strings"
	"time"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// CVERule CVE规则结构
type CVERule struct {
	ID              uint      `gorm:"primarykey" json:"id"`
	CVEID           string    `gorm:"uniqueIndex;size:20;not null" json:"cve_id"`
	Name            string    `gorm:"size:255;not null" json:"name"`
	Description     string    `gorm:"type:text" json:"description"`
	Severity        string    `gorm:"size:20;default:medium" json:"severity"`
	CVSSScore       float64   `gorm:"default:0" json:"cvss_score"`
	CVSSVector      string    `gorm:"size:255" json:"cvss_vector"`
	PublishedDate   time.Time `json:"published_date"`
	ModifiedDate    time.Time `json:"modified_date"`
	AffectedProducts string   `gorm:"type:text" json:"affected_products"`
	References      string    `gorm:"type:text" json:"references"`
	Pattern         string    `gorm:"type:text" json:"pattern"`
	Signature       string    `gorm:"type:text" json:"signature"`
	Payload         string    `gorm:"type:text" json:"payload"`
	Method          string    `gorm:"size:20" json:"method"`
	Path            string    `gorm:"size:255" json:"path"`
	Headers         string    `gorm:"type:text" json:"headers"`
	Body            string    `gorm:"type:text" json:"body"`
	StatusCode      string    `gorm:"size:20" json:"status_code"`
	ResponsePattern string    `gorm:"type:text" json:"response_pattern"`
	Conditions      string    `gorm:"type:text" json:"conditions"`
	Tags            string    `gorm:"type:text" json:"tags"`
	Enabled         bool      `gorm:"default:true" json:"enabled"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
}

// CVERuleFile CVE规则文件结构
type CVERuleFile struct {
	ID               string    `json:"id"`
	Name             string    `json:"name"`
	Description      string    `json:"description"`
	Severity         string    `json:"severity"`
	CVSSScore        float64   `json:"cvss_score"`
	CVSSVector       string    `json:"cvss_vector"`
	PublishedDate    string    `json:"published_date"`
	ModifiedDate     string    `json:"modified_date"`
	AffectedProducts []string  `json:"affected_products"`
	References       []string  `json:"references"`
	Detection        Detection `json:"detection"`
	Tags             []string  `json:"tags"`
}

// Detection 检测规则结构
type Detection struct {
	Pattern         string            `json:"pattern"`
	Signature       string            `json:"signature"`
	Payload         string            `json:"payload"`
	Method          string            `json:"method"`
	Path            string            `json:"path"`
	Headers         map[string]string `json:"headers"`
	Body            string            `json:"body"`
	StatusCode      string            `json:"status_code"`
	ResponsePattern string            `json:"response_pattern"`
	Conditions      []string          `json:"conditions"`
}

func main() {
	fmt.Println("🚀 开始初始化CVE数据库...")

	// 连接数据库
	db, err := gorm.Open(sqlite.Open("data/cve.db"), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})
	if err != nil {
		log.Fatalf("❌ 连接CVE数据库失败: %v", err)
	}

	// 自动迁移
	fmt.Println("📊 创建CVE数据库表...")
	if err := db.AutoMigrate(&CVERule{}); err != nil {
		log.Fatalf("❌ 创建CVE表失败: %v", err)
	}

	// 创建索引
	fmt.Println("🔍 创建数据库索引...")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_cve_rules_severity ON cve_rules(severity)")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_cve_rules_enabled ON cve_rules(enabled)")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_cve_rules_cvss_score ON cve_rules(cvss_score)")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_cve_rules_published_date ON cve_rules(published_date)")

	// 清空现有数据
	fmt.Println("🗑️ 清空现有CVE数据...")
	db.Exec("DELETE FROM cve_rules")

	// 加载CVE规则文件
	cveDir := "rules/cve"
	if _, err := os.Stat(cveDir); os.IsNotExist(err) {
		log.Fatalf("❌ CVE规则目录不存在: %s", cveDir)
	}

	files, err := ioutil.ReadDir(cveDir)
	if err != nil {
		log.Fatalf("❌ 读取CVE规则目录失败: %v", err)
	}

	fmt.Printf("📋 发现 %d 个CVE规则文件\n", len(files))

	successCount := 0
	errorCount := 0

	for _, file := range files {
		if !strings.HasSuffix(file.Name(), ".json") {
			continue
		}

		filePath := filepath.Join(cveDir, file.Name())
		if err := loadCVERule(db, filePath); err != nil {
			fmt.Printf("❌ 加载CVE规则失败 %s: %v\n", file.Name(), err)
			errorCount++
		} else {
			successCount++
		}
	}

	fmt.Printf("✅ CVE数据库初始化完成!\n")
	fmt.Printf("   成功加载: %d 个CVE规则\n", successCount)
	fmt.Printf("   失败数量: %d 个CVE规则\n", errorCount)

	// 统计信息
	var totalCount int64
	db.Model(&CVERule{}).Count(&totalCount)
	fmt.Printf("   数据库总计: %d 个CVE规则\n", totalCount)

	// 按严重程度统计
	var severityStats []struct {
		Severity string
		Count    int64
	}
	db.Model(&CVERule{}).Select("severity, count(*) as count").Group("severity").Scan(&severityStats)
	
	fmt.Println("\n📊 CVE严重程度分布:")
	for _, stat := range severityStats {
		fmt.Printf("   %s: %d 个\n", stat.Severity, stat.Count)
	}

	// 按年份统计
	var yearStats []struct {
		Year  string
		Count int64
	}
	db.Model(&CVERule{}).Select("substr(cve_id, 5, 4) as year, count(*) as count").Group("year").Order("year").Scan(&yearStats)
	
	fmt.Println("\n📅 CVE年份分布:")
	for _, stat := range yearStats {
		fmt.Printf("   %s: %d 个\n", stat.Year, stat.Count)
	}
}

func loadCVERule(db *gorm.DB, filePath string) error {
	// 读取文件
	data, err := ioutil.ReadFile(filePath)
	if err != nil {
		return fmt.Errorf("读取文件失败: %v", err)
	}

	// 解析JSON
	var cveFile CVERuleFile
	if err := json.Unmarshal(data, &cveFile); err != nil {
		return fmt.Errorf("解析JSON失败: %v", err)
	}

	// 解析日期
	publishedDate, _ := time.Parse("2006-01-02", cveFile.PublishedDate)
	modifiedDate, _ := time.Parse("2006-01-02", cveFile.ModifiedDate)

	// 转换为数据库模型
	cveRule := CVERule{
		CVEID:           cveFile.ID,
		Name:            cveFile.Name,
		Description:     cveFile.Description,
		Severity:        cveFile.Severity,
		CVSSScore:       cveFile.CVSSScore,
		CVSSVector:      cveFile.CVSSVector,
		PublishedDate:   publishedDate,
		ModifiedDate:    modifiedDate,
		AffectedProducts: strings.Join(cveFile.AffectedProducts, ", "),
		References:      strings.Join(cveFile.References, "\n"),
		Pattern:         cveFile.Detection.Pattern,
		Signature:       cveFile.Detection.Signature,
		Payload:         cveFile.Detection.Payload,
		Method:          cveFile.Detection.Method,
		Path:            cveFile.Detection.Path,
		Body:            cveFile.Detection.Body,
		StatusCode:      cveFile.Detection.StatusCode,
		ResponsePattern: cveFile.Detection.ResponsePattern,
		Tags:            strings.Join(cveFile.Tags, ", "),
		Enabled:         true,
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}

	// 处理Headers
	if len(cveFile.Detection.Headers) > 0 {
		headersJSON, _ := json.Marshal(cveFile.Detection.Headers)
		cveRule.Headers = string(headersJSON)
	}

	// 处理Conditions
	if len(cveFile.Detection.Conditions) > 0 {
		conditionsJSON, _ := json.Marshal(cveFile.Detection.Conditions)
		cveRule.Conditions = string(conditionsJSON)
	}

	// 保存到数据库
	if err := db.Create(&cveRule).Error; err != nil {
		return fmt.Errorf("保存到数据库失败: %v", err)
	}

	return nil
}
