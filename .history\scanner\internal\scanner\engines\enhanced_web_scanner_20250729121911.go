package engines

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"scanner/internal/scanner/rules"
	"scanner/internal/scanner/types"
	"scanner/internal/services"
	"scanner/internal/services/vulnerability"
	"scanner/pkg/logger"
)

// EnhancedWebScanner 增强Web扫描引擎
// 集成信息收集、漏洞检测等完整功能
type EnhancedWebScanner struct {
	name                 string
	enabled              bool
	infoGatheringService *services.InfoGatheringService
	scanLogService       *services.ScanLogService
	fingerprintEngine    *EnhancedFingerprintEngine
	directoryScanner     *EnhancedDirectoryScanner
	ruleManager          *rules.SimpleRuleManager
	vulnService          *vulnerability.Service
	stats                *EnhancedWebStats
}

// EnhancedWebStats 增强Web扫描统计
type EnhancedWebStats struct {
	TotalScans           int64         `json:"total_scans"`
	FingerprintsFound    int64         `json:"fingerprints_found"`
	DirectoriesFound     int64         `json:"directories_found"`
	VulnerabilitiesFound int64         `json:"vulnerabilities_found"`
	AverageScanDuration  time.Duration `json:"average_scan_duration"`
	LastScanTime         time.Time     `json:"last_scan_time"`
}

// NewEnhancedWebScanner 创建增强Web扫描引擎
func NewEnhancedWebScanner(infoGatheringService *services.InfoGatheringService, scanLogService *services.ScanLogService, vulnService *vulnerability.Service) *EnhancedWebScanner {
	fingerprintEngine := NewEnhancedFingerprintEngine(infoGatheringService, scanLogService)
	directoryScanner := NewEnhancedDirectoryScanner(infoGatheringService, scanLogService)

	// 初始化规则管理器
	ruleManager := rules.NewSimpleRuleManager("rules", vulnService, scanLogService)

	return &EnhancedWebScanner{
		name:                 "enhanced_web_scanner",
		enabled:              true,
		infoGatheringService: infoGatheringService,
		scanLogService:       scanLogService,
		fingerprintEngine:    fingerprintEngine,
		directoryScanner:     directoryScanner,
		ruleManager:          ruleManager,
		vulnService:          vulnService,
		stats:                &EnhancedWebStats{},
	}
}

// GetName 获取引擎名称
func (e *EnhancedWebScanner) GetName() string {
	return e.name
}

// GetType 获取引擎类型
func (e *EnhancedWebScanner) GetType() string {
	return "web"
}

// IsEnabled 检查引擎是否启用
func (e *EnhancedWebScanner) IsEnabled() bool {
	return e.enabled
}

// GetSupportedTargets 获取支持的目标类型
func (e *EnhancedWebScanner) GetSupportedTargets() []string {
	return []string{"url", "domain", "ip"}
}

// Scan 执行完整的Web扫描
func (e *EnhancedWebScanner) Scan(ctx context.Context, target *types.ScanTarget, config *types.ScanConfig, progress chan<- *types.ScanProgress) (*types.ScanResult, error) {
	startTime := time.Now()
	logger.Infof("🌐 开始增强Web扫描: %s", target.Value)

	// 更新统计
	e.stats.TotalScans++
	e.stats.LastScanTime = startTime

	// 创建扫描结果
	result := &types.ScanResult{
		TaskID:          fmt.Sprintf("enhanced-web-%d", time.Now().Unix()),
		TargetID:        target.Value,
		Status:          "running",
		Metadata:        make(map[string]interface{}),
		StartTime:       startTime,
		Vulnerabilities: make([]*types.Vulnerability, 0),
		Errors:          make([]string, 0),
	}

	// 从配置中获取任务ID
	var taskID uint
	if config != nil && config.TaskID != "" {
		if id, err := strconv.ParseUint(config.TaskID, 10, 32); err == nil {
			taskID = uint(id)
		} else {
			logger.Warnf("无法解析任务ID: %s, 使用默认值", config.TaskID)
			taskID = 1
		}
	}

	// 第一阶段：信息收集 (0-60%)
	logger.Info("📊 第一阶段：信息收集")
	if e.scanLogService != nil {
		e.scanLogService.LogInfo(taskID, "Web扫描", target.Value, "开始信息收集阶段", 5)
	}

	// 指纹识别 (5-30%)
	if progress != nil {
		progress <- &types.ScanProgress{
			TaskID:      result.TaskID,
			Progress:    10,
			CurrentItem: "指纹识别",
			Message:     "开始指纹识别",
			Timestamp:   time.Now(),
		}
	}

	fingerprintResults, err := e.fingerprintEngine.IdentifyTechnologies(ctx, taskID, target.Value)
	if err != nil {
		logger.Errorf("指纹识别失败: %v", err)
		result.Errors = append(result.Errors, fmt.Sprintf("指纹识别失败: %v", err))
	} else {
		e.stats.FingerprintsFound += int64(len(fingerprintResults))
		result.Metadata["fingerprints"] = fingerprintResults
		logger.Infof("指纹识别完成，发现 %d 个技术", len(fingerprintResults))
	}

	// 目录发现 (30-60%)
	if progress != nil {
		progress <- &types.ScanProgress{
			TaskID:      result.TaskID,
			Progress:    40,
			CurrentItem: "目录发现",
			Message:     "开始目录发现",
			Timestamp:   time.Now(),
		}
	}

	directoryResults, err := e.directoryScanner.ScanDirectories(ctx, taskID, target.Value)
	if err != nil {
		logger.Errorf("目录发现失败: %v", err)
		result.Errors = append(result.Errors, fmt.Sprintf("目录发现失败: %v", err))
	} else {
		e.stats.DirectoriesFound += int64(len(directoryResults))
		result.Metadata["directories"] = directoryResults
		logger.Infof("目录发现完成，发现 %d 个可访问目录", len(directoryResults))
	}

	// 第二阶段：漏洞检测 (60-90%)
	logger.Info("🔍 第二阶段：漏洞检测")
	if e.scanLogService != nil {
		e.scanLogService.LogInfo(taskID, "Web扫描", target.Value, "开始漏洞检测阶段", 65)
	}

	if progress != nil {
		progress <- &types.ScanProgress{
			TaskID:      result.TaskID,
			Progress:    70,
			CurrentItem: "漏洞检测",
			Message:     "基于指纹信息进行智能漏洞检测",
			Timestamp:   time.Now(),
		}
	}

	// 基于指纹信息进行智能漏洞检测
	vulnerabilities := e.performIntelligentVulnDetection(ctx, taskID, target.Value, fingerprintResults, directoryResults)

	// 执行规则匹配检测
	ruleVulnerabilities := e.performRuleBasedDetection(ctx, taskID, target.Value)
	vulnerabilities = append(vulnerabilities, ruleVulnerabilities...)

	result.Vulnerabilities = vulnerabilities
	e.stats.VulnerabilitiesFound += int64(len(vulnerabilities))

	// 第三阶段：结果汇总 (90-100%)
	logger.Info("📋 第三阶段：结果汇总")
	if e.scanLogService != nil {
		e.scanLogService.LogInfo(taskID, "Web扫描", target.Value, "汇总扫描结果", 95)
	}

	if progress != nil {
		progress <- &types.ScanProgress{
			TaskID:      result.TaskID,
			Progress:    95,
			CurrentItem: "结果汇总",
			Message:     "汇总扫描结果",
			Timestamp:   time.Now(),
		}
	}

	// 生成扫描报告
	report := e.generateScanReport(fingerprintResults, directoryResults, vulnerabilities)
	result.Metadata["scan_report"] = report

	// 构建信息收集数据结构
	infoGatheringData := e.buildInfoGatheringData(target.Value, fingerprintResults, directoryResults)
	result.Metadata["info_gathering_data"] = infoGatheringData

	// 保存信息收集数据到数据库
	e.saveInfoGatheringDataToDB(taskID, infoGatheringData)

	// 完成扫描
	result.Status = "completed"
	result.EndTime = time.Now()
	duration := result.EndTime.Sub(result.StartTime)
	e.stats.AverageScanDuration = duration

	// 更新结果元数据
	result.Metadata["scan_duration"] = duration.String()
	result.Metadata["fingerprints_count"] = len(fingerprintResults)
	result.Metadata["directories_count"] = len(directoryResults)
	result.Metadata["vulnerabilities_count"] = len(vulnerabilities)
	result.Metadata["engine_type"] = "enhanced_web_scanner"

	if progress != nil {
		progress <- &types.ScanProgress{
			TaskID:      result.TaskID,
			Progress:    100,
			CurrentItem: "完成",
			Message:     "Web扫描完成",
			Timestamp:   time.Now(),
		}
	}

	logger.Infof("✅ 增强Web扫描完成: %s (耗时: %v)", target.Value, duration)
	if e.scanLogService != nil {
		e.scanLogService.LogInfo(taskID, "Web扫描", target.Value,
			fmt.Sprintf("Web扫描完成 (耗时: %v)", duration), 100)
	}

	return result, nil
}

// performIntelligentVulnDetection 基于指纹信息进行智能漏洞检测
func (e *EnhancedWebScanner) performIntelligentVulnDetection(ctx context.Context, taskID uint, target string, fingerprints []*FingerprintResult, directories []*DirectoryResult) []*types.Vulnerability {
	var vulnerabilities []*types.Vulnerability

	logger.Info("🧠 开始智能漏洞检测")

	// 基于指纹信息进行针对性检测
	for _, fp := range fingerprints {
		vulns := e.detectVulnerabilitiesForTechnology(ctx, taskID, target, fp)
		vulnerabilities = append(vulnerabilities, vulns...)
	}

	// 基于目录信息进行检测
	for _, dir := range directories {
		vulns := e.detectVulnerabilitiesForDirectory(ctx, taskID, target, dir)
		vulnerabilities = append(vulnerabilities, vulns...)
	}

	logger.Infof("智能漏洞检测完成，发现 %d 个潜在漏洞", len(vulnerabilities))
	return vulnerabilities
}

// detectVulnerabilitiesForTechnology 针对特定技术进行漏洞检测
func (e *EnhancedWebScanner) detectVulnerabilitiesForTechnology(ctx context.Context, taskID uint, target string, fingerprint *FingerprintResult) []*types.Vulnerability {
	var vulnerabilities []*types.Vulnerability

	// 根据技术类型进行针对性检测
	switch fingerprint.Technology {
	case "PHP":
		// PHP相关漏洞检测
		vulnerabilities = append(vulnerabilities, e.detectPHPVulnerabilities(ctx, taskID, target, fingerprint)...)
	case "WordPress":
		// WordPress相关漏洞检测
		vulnerabilities = append(vulnerabilities, e.detectWordPressVulnerabilities(ctx, taskID, target, fingerprint)...)
	case "Apache":
		// Apache相关漏洞检测
		vulnerabilities = append(vulnerabilities, e.detectApacheVulnerabilities(ctx, taskID, target, fingerprint)...)
	}

	return vulnerabilities
}

// detectVulnerabilitiesForDirectory 针对特定目录进行漏洞检测
func (e *EnhancedWebScanner) detectVulnerabilitiesForDirectory(ctx context.Context, taskID uint, target string, directory *DirectoryResult) []*types.Vulnerability {
	var vulnerabilities []*types.Vulnerability

	// 根据目录路径进行针对性检测
	switch directory.Path {
	case "/admin", "/administrator":
		// 管理后台相关检测
		vulnerabilities = append(vulnerabilities, e.detectAdminPanelVulnerabilities(ctx, taskID, target, directory)...)
	case "/backup", "/backups":
		// 备份文件相关检测
		vulnerabilities = append(vulnerabilities, e.detectBackupFileVulnerabilities(ctx, taskID, target, directory)...)
	}

	return vulnerabilities
}

// 具体的漏洞检测方法（示例实现）
func (e *EnhancedWebScanner) detectPHPVulnerabilities(ctx context.Context, taskID uint, target string, fingerprint *FingerprintResult) []*types.Vulnerability {
	// 这里实现PHP相关的漏洞检测逻辑
	return []*types.Vulnerability{}
}

func (e *EnhancedWebScanner) detectWordPressVulnerabilities(ctx context.Context, taskID uint, target string, fingerprint *FingerprintResult) []*types.Vulnerability {
	// 这里实现WordPress相关的漏洞检测逻辑
	return []*types.Vulnerability{}
}

func (e *EnhancedWebScanner) detectApacheVulnerabilities(ctx context.Context, taskID uint, target string, fingerprint *FingerprintResult) []*types.Vulnerability {
	// 这里实现Apache相关的漏洞检测逻辑
	return []*types.Vulnerability{}
}

func (e *EnhancedWebScanner) detectAdminPanelVulnerabilities(ctx context.Context, taskID uint, target string, directory *DirectoryResult) []*types.Vulnerability {
	// 这里实现管理后台相关的漏洞检测逻辑
	return []*types.Vulnerability{}
}

func (e *EnhancedWebScanner) detectBackupFileVulnerabilities(ctx context.Context, taskID uint, target string, directory *DirectoryResult) []*types.Vulnerability {
	// 这里实现备份文件相关的漏洞检测逻辑
	return []*types.Vulnerability{}
}

// generateScanReport 生成扫描报告
func (e *EnhancedWebScanner) generateScanReport(fingerprints []*FingerprintResult, directories []*DirectoryResult, vulnerabilities []*types.Vulnerability) map[string]interface{} {
	report := make(map[string]interface{})

	// 指纹识别汇总
	fingerprintSummary := make(map[string][]string)
	for _, fp := range fingerprints {
		if _, exists := fingerprintSummary[fp.Category]; !exists {
			fingerprintSummary[fp.Category] = make([]string, 0)
		}
		tech := fp.Technology
		if fp.Version != "" {
			tech += " " + fp.Version
		}
		fingerprintSummary[fp.Category] = append(fingerprintSummary[fp.Category], tech)
	}
	report["fingerprint_summary"] = fingerprintSummary

	// 目录发现汇总
	directorySummary := make(map[string][]string)
	for _, dir := range directories {
		statusGroup := fmt.Sprintf("%dxx", dir.StatusCode/100)
		if _, exists := directorySummary[statusGroup]; !exists {
			directorySummary[statusGroup] = make([]string, 0)
		}
		directorySummary[statusGroup] = append(directorySummary[statusGroup], dir.Path)
	}
	report["directory_summary"] = directorySummary

	// 漏洞汇总
	vulnSummary := make(map[string]int)
	for _, vuln := range vulnerabilities {
		vulnSummary[vuln.Severity]++
	}
	report["vulnerability_summary"] = vulnSummary

	// 统计信息
	report["statistics"] = map[string]interface{}{
		"total_fingerprints":    len(fingerprints),
		"total_directories":     len(directories),
		"total_vulnerabilities": len(vulnerabilities),
		"scan_timestamp":        time.Now(),
	}

	return report
}

// IsHealthy 检查引擎健康状态
func (e *EnhancedWebScanner) IsHealthy() bool {
	return e.enabled
}

// GetStats 获取统计信息
func (e *EnhancedWebScanner) GetStats() interface{} {
	return e.stats
}

// SetLogService 设置日志服务
func (e *EnhancedWebScanner) SetLogService(logService *services.ScanLogService) {
	e.scanLogService = logService
}

// Validate 验证配置
func (e *EnhancedWebScanner) Validate(config *types.ScanConfig) error {
	if config == nil {
		return fmt.Errorf("扫描配置不能为空")
	}
	return nil
}

// performRuleBasedDetection 执行基于规则的漏洞检测
func (e *EnhancedWebScanner) performRuleBasedDetection(ctx context.Context, taskID uint, target string) []*types.Vulnerability {
	var vulnerabilities []*types.Vulnerability

	if e.ruleManager == nil || !e.ruleManager.IsEnabled() {
		logger.Debug("规则管理器未启用，跳过规则检测")
		return vulnerabilities
	}

	logger.Info("🔍 开始基于规则的漏洞检测")
	if e.scanLogService != nil {
		e.scanLogService.LogInfo(taskID, "规则检测", target, "开始规则匹配检测", 75)
	}

	// 发送HTTP请求获取响应
	response, err := e.sendHTTPRequest(target)
	if err != nil {
		logger.Errorf("发送HTTP请求失败: %v", err)
		return vulnerabilities
	}
	defer response.Body.Close()

	// 执行规则匹配
	matches, err := e.ruleManager.MatchWebRules(ctx, target, nil, response)
	if err != nil {
		logger.Errorf("规则匹配失败: %v", err)
		return vulnerabilities
	}

	// 将匹配结果转换为漏洞
	for _, match := range matches {
		if err := e.ruleManager.CreateVulnerabilityFromRule(taskID, match); err != nil {
			logger.Errorf("创建规则漏洞失败: %v", err)
			continue
		}

		// 创建漏洞对象用于返回
		vuln := &types.Vulnerability{
			ID:          fmt.Sprintf("rule-%s-%d", match.RuleID, time.Now().UnixNano()),
			Type:        match.RuleType,
			Name:        match.RuleName,
			Description: fmt.Sprintf("规则检测发现的漏洞: %s", match.RuleName),
			Severity:    match.Severity,
			URL:         target,
			Evidence:    match.Evidence,
			Solution:    "请根据漏洞类型采取相应的修复措施",
			CreatedAt:   time.Now(),
		}

		vulnerabilities = append(vulnerabilities, vuln)

		logger.Infof("规则匹配发现漏洞: %s (严重程度: %s)", match.RuleName, match.Severity)
	}

	logger.Infof("规则检测完成，发现 %d 个漏洞", len(vulnerabilities))
	if e.scanLogService != nil {
		e.scanLogService.LogInfo(taskID, "规则检测", target,
			fmt.Sprintf("规则检测完成，发现 %d 个漏洞", len(vulnerabilities)), 80)
	}

	return vulnerabilities
}

// buildInfoGatheringData 构建信息收集数据结构
func (e *EnhancedWebScanner) buildInfoGatheringData(target string, fingerprintResults []*FingerprintResult, directoryResults []*DirectoryResult) map[string]interface{} {
	// 构建技术栈信息
	techStack := e.buildTechStackFromFingerprints(fingerprintResults)

	// 构建基本信息
	basicInfo := map[string]interface{}{
		"url":    target,
		"domain": e.extractDomainFromURL(target),
		"ip":     "", // 可以从指纹识别结果中获取
	}

	// 构建目录结构信息
	directoryStructure := e.buildDirectoryStructure(directoryResults)

	// 构建服务信息
	services := map[string]interface{}{
		"web_service": map[string]interface{}{
			"detected": true,
			"url":      target,
		},
	}

	// 构建统计信息
	statistics := map[string]interface{}{
		"fingerprints_found": len(fingerprintResults),
		"directories_found":  len(directoryResults),
		"scan_time":          time.Now().Format("2006-01-02 15:04:05"),
	}

	return map[string]interface{}{
		"basic_info":          basicInfo,
		"tech_stack":          techStack,
		"services":            services,
		"directory_structure": directoryStructure,
		"statistics":          statistics,
	}
}

// buildTechStackFromFingerprints 从指纹识别结果构建技术栈信息
func (e *EnhancedWebScanner) buildTechStackFromFingerprints(fingerprintResults []*FingerprintResult) map[string]interface{} {
	techStack := map[string]interface{}{
		"web_server":   "",
		"framework":    "",
		"language":     "",
		"database":     "",
		"cms":          "",
		"technologies": []string{},
	}

	for _, fingerprint := range fingerprintResults {
		technology := fingerprint.Technology
		category := fingerprint.Category

		switch category {
		case "web_server", "server":
			techStack["web_server"] = technology
		case "framework":
			techStack["framework"] = technology
		case "language", "programming_language":
			techStack["language"] = technology
		case "database":
			techStack["database"] = technology
		case "cms":
			techStack["cms"] = technology
		default:
			// 添加到库列表
			if libraries, ok := techStack["libraries"].([]string); ok {
				techStack["libraries"] = append(libraries, technology)
			}
		}
	}

	return techStack
}

// buildDirectoryStructure 构建目录结构信息
func (e *EnhancedWebScanner) buildDirectoryStructure(directoryResults []*DirectoryResult) map[string]interface{} {
	directories := make([]map[string]interface{}, 0)

	for _, dir := range directoryResults {
		dirInfo := map[string]interface{}{
			"path":        dir.Path,
			"status_code": dir.StatusCode,
			"size":        dir.Size,
			"title":       dir.Title,
			"server":      dir.Server,
			"accessible":  true,
		}
		directories = append(directories, dirInfo)
	}

	return map[string]interface{}{
		"accessible_directories": directories,
		"total_directories":      len(directories),
	}
}

// extractDomainFromURL 从URL中提取域名
func (e *EnhancedWebScanner) extractDomainFromURL(urlStr string) string {
	if strings.HasPrefix(urlStr, "http://") || strings.HasPrefix(urlStr, "https://") {
		parts := strings.Split(urlStr, "/")
		if len(parts) >= 3 {
			return parts[2]
		}
	}
	return urlStr
}

// saveInfoGatheringDataToDB 保存信息收集数据到数据库
func (e *EnhancedWebScanner) saveInfoGatheringDataToDB(taskID uint, infoData map[string]interface{}) {
	if e.vulnService == nil {
		logger.Warn("漏洞服务未初始化，无法保存信息收集数据")
		return
	}

	// 将信息收集数据转换为JSON字符串
	infoDataJSON, err := json.Marshal(infoData)
	if err != nil {
		logger.Errorf("序列化信息收集数据失败: %v", err)
		return
	}

	// 调用漏洞服务更新任务的信息收集数据
	taskIDStr := fmt.Sprintf("%d", taskID)
	if err := e.vulnService.UpdateTaskInfoGatheringData(taskIDStr, string(infoDataJSON)); err != nil {
		logger.Errorf("保存信息收集数据到数据库失败: %v", err)
		return
	}

	logger.Infof("成功保存任务 %d 的信息收集数据到数据库", taskID)
}

// sendHTTPRequest 发送HTTP请求
func (e *EnhancedWebScanner) sendHTTPRequest(target string) (*http.Response, error) {
	// 创建HTTP客户端
	client := &http.Client{
		Timeout: 30 * time.Second,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			// 允许最多5次重定向
			if len(via) >= 5 {
				return fmt.Errorf("重定向次数过多")
			}
			return nil
		},
	}

	// 创建请求
	req, err := http.NewRequest("GET", target, nil)
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %v", err)
	}

	// 设置User-Agent
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Accept-Encoding", "gzip, deflate")
	req.Header.Set("Connection", "keep-alive")

	// 发送请求
	response, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送HTTP请求失败: %v", err)
	}

	return response, nil
}

// Stop 停止引擎
func (e *EnhancedWebScanner) Stop(ctx context.Context, taskID string) error {
	logger.Infof("增强Web扫描引擎已停止，任务ID: %s", taskID)
	return nil
}
