package engines

import (
	"context"
	"fmt"
	"strings"
	"time"

	"scanner/internal/models"
	"scanner/internal/scanner/intelligence"
	"scanner/internal/scanner/types"
	"scanner/internal/services"
	"scanner/pkg/logger"
)

// IntelligentWebEngine 智能Web扫描引擎
// 基于指纹识别和智能策略的高效Web漏洞扫描
type IntelligentWebEngine struct {
	name                 string
	enabled              bool
	fingerprintEngine    *intelligence.FingerprintEngine
	strategyEngine       *intelligence.DetectionStrategy
	detectors            map[string]VulnerabilityDetector
	infoGatheringService *services.InfoGatheringService // 新增信息收集服务
	scanLogService       *services.ScanLogService       // 新增扫描日志服务
	stats                *IntelligentEngineStats
}

// IntelligentEngineStats 智能引擎统计
type IntelligentEngineStats struct {
	TotalScans           int           `json:"total_scans"`
	SuccessfulScans      int           `json:"successful_scans"`
	TechnologiesFound    int           `json:"technologies_found"`
	VulnerabilitiesFound int           `json:"vulnerabilities_found"`
	AverageReduction     float64       `json:"average_reduction"` // 平均检测数量减少比例
	AverageScanTime      time.Duration `json:"average_scan_time"`
	LastScanTime         time.Time     `json:"last_scan_time"`
}

// NewIntelligentWebEngine 创建智能Web扫描引擎
func NewIntelligentWebEngine() *IntelligentWebEngine {
	fingerprintEngine := intelligence.NewFingerprintEngine()
	fingerprintEngine.LoadRules()

	strategyEngine := intelligence.NewDetectionStrategy(fingerprintEngine)

	engine := &IntelligentWebEngine{
		name:              "intelligent_web",
		enabled:           true,
		fingerprintEngine: fingerprintEngine,
		strategyEngine:    strategyEngine,
		detectors:         make(map[string]VulnerabilityDetector),
		stats: &IntelligentEngineStats{
			AverageReduction: 0.0,
		},
	}

	// 注册检测器
	engine.registerDetectors()

	return engine
}

// Scan 执行智能Web扫描
func (e *IntelligentWebEngine) Scan(ctx context.Context, target string, config map[string]interface{}) (*types.ScanResult, error) {
	startTime := time.Now()
	logger.Infof("开始智能Web扫描: %s", target)

	// 更新统计
	e.stats.TotalScans++
	e.stats.LastScanTime = startTime

	result := &types.ScanResult{
		TaskID:          "", // 将在后续设置
		TargetID:        target,
		StartTime:       startTime,
		Vulnerabilities: make([]*types.Vulnerability, 0),
		Metadata:        make(map[string]interface{}),
	}

	// 1. 生成智能检测计划
	plan, err := e.strategyEngine.GenerateDetectionPlan(ctx, target)
	if err != nil {
		return nil, fmt.Errorf("生成检测计划失败: %v", err)
	}

	// 记录检测计划信息
	result.Metadata["detection_plan"] = plan
	result.Metadata["technologies_identified"] = len(plan.Technologies)
	result.Metadata["strategies_selected"] = len(plan.Strategies)

	logger.Infof("检测计划: 识别到 %d 个技术, 选择 %d 个策略, 预估时间 %d 秒",
		len(plan.Technologies), len(plan.Strategies), plan.EstimatedTime)

	// 2. 按计划执行智能检测
	vulnerabilities, err := e.executeDetectionPlan(ctx, plan)
	if err != nil {
		return nil, fmt.Errorf("执行检测计划失败: %v", err)
	}

	// 转换models.Vulnerability到types.Vulnerability
	typeVulns := make([]*types.Vulnerability, len(vulnerabilities))
	for i, v := range vulnerabilities {
		// 解析References字符串为字符串数组
		var refs []string
		if v.References != "" {
			refs = []string{v.References}
		}

		typeVulns[i] = &types.Vulnerability{
			ID:          fmt.Sprintf("%d", v.ID),
			Name:        v.Name,
			Type:        v.Type,
			Severity:    v.Severity,
			Description: v.Description,
			URL:         v.URL,
			Method:      v.Method,
			Parameter:   v.Parameter,
			Payload:     v.Payload,
			Evidence:    v.Evidence,
			Solution:    v.Solution,
			References:  refs,
			CVSS:        v.CVSS,
		}
	}
	result.Vulnerabilities = typeVulns
	result.EndTime = time.Now()
	result.Duration = result.EndTime.Sub(result.StartTime)

	// 3. 更新统计信息
	e.updateStats(plan, len(vulnerabilities), result.Duration)

	logger.Infof("智能Web扫描完成: 发现 %d 个漏洞, 耗时 %v", len(vulnerabilities), result.Duration)

	return result, nil
}

// executeDetectionPlan 执行检测计划
func (e *IntelligentWebEngine) executeDetectionPlan(ctx context.Context, plan *intelligence.DetectionPlan) ([]*models.Vulnerability, error) {
	var allVulnerabilities []*models.Vulnerability

	// 按优先级顺序执行检测策略
	for i, strategy := range plan.Strategies {
		logger.Infof("执行检测策略 %d/%d: %s - %s", i+1, len(plan.Strategies), strategy.Technology, strategy.VulnType)

		// 检查上下文是否被取消
		select {
		case <-ctx.Done():
			return allVulnerabilities, ctx.Err()
		default:
		}

		// 获取对应的检测器
		detector, exists := e.detectors[strategy.Detector]
		if !exists {
			logger.Warnf("检测器 %s 不存在，跳过", strategy.Detector)
			continue
		}

		// 构造检测目标
		target := &types.DetectionTarget{
			URL:      plan.Target,
			Method:   "GET",
			Headers:  make(map[string]string),
			Body:     "",
			Payloads: strategy.Payloads,
			Config:   strategy.Config,
		}

		// 转换types.DetectionTarget到engines.DetectionTarget
		engineTarget := &DetectionTarget{
			URL:        target.URL,
			Method:     target.Method,
			Headers:    target.Headers,
			Body:       target.Body,
			Parameters: make(map[string]string),
			Metadata:   target.Config,
		}

		// 执行检测
		results, err := detector.Detect(ctx, engineTarget)
		if err != nil {
			logger.Warnf("检测器 %s 执行失败: %v", strategy.Detector, err)
			continue
		}

		// 转换检测结果为漏洞记录
		for _, result := range results {
			// 转换engines.DetectionResult到types.DetectionResult
			typeResult := &types.DetectionResult{
				ID:          result.ID,
				VulnType:    result.VulnType,
				Severity:    result.Severity,
				Confidence:  result.Confidence,
				Payload:     result.Payload,
				Evidence:    strings.Join(result.Evidence, "; "),
				URL:         target.URL,
				Method:      target.Method,
				Parameter:   "",
				Description: "",
				Solution:    "",
				References:  []string{},
			}
			vuln := e.convertDetectionResultToVulnerability(typeResult, strategy)
			allVulnerabilities = append(allVulnerabilities, vuln)
		}

		logger.Infof("检测器 %s 发现 %d 个漏洞", strategy.Detector, len(results))

		// 转换结果用于提前停止检测判断
		typeResults := make([]*types.DetectionResult, len(results))
		for i, result := range results {
			typeResults[i] = &types.DetectionResult{
				ID:         result.ID,
				VulnType:   result.VulnType,
				Severity:   result.Severity,
				Confidence: result.Confidence,
			}
		}

		// 如果发现高危漏洞，可以选择提前结束某些检测
		if e.shouldStopEarlyDetection(typeResults, strategy) {
			logger.Infof("发现高危漏洞，跳过后续低优先级检测")
			break
		}
	}

	return allVulnerabilities, nil
}

// shouldStopEarlyDetection 判断是否应该提前停止检测
func (e *IntelligentWebEngine) shouldStopEarlyDetection(results []*types.DetectionResult, strategy *intelligence.SelectedStrategy) bool {
	// 如果发现严重漏洞且当前策略优先级较低，可以考虑提前停止
	for _, result := range results {
		if result.Severity == "critical" && strategy.Priority < 7 {
			return true
		}
	}
	return false
}

// convertDetectionResultToVulnerability 转换检测结果为漏洞记录
func (e *IntelligentWebEngine) convertDetectionResultToVulnerability(result *types.DetectionResult, strategy *intelligence.SelectedStrategy) *models.Vulnerability {
	return &models.Vulnerability{
		Name:        result.VulnType,
		Type:        result.VulnType,
		Title:       result.Description,
		Category:    strategy.VulnType,
		Description: result.Description,
		Severity:    result.Severity,
		CVSS:        e.getCVSSFromSeverity(result.Severity),
		URL:         result.URL,
		Method:      result.Method,
		Parameter:   result.Parameter,
		Payload:     result.Payload,
		Evidence:    result.Evidence,
		Solution:    result.Solution,
		References:  fmt.Sprintf(`["%s"]`, strings.Join(result.References, `", "`)),
		Status:      "open",
		Metadata: fmt.Sprintf(`{"strategy": "%s", "technology": "%s", "reason": "%s", "confidence": %.2f}`,
			strategy.VulnType, strategy.Technology, strategy.Reason, result.Confidence),
	}
}

// getCVSSFromSeverity 根据严重程度获取CVSS评分
func (e *IntelligentWebEngine) getCVSSFromSeverity(severity string) float64 {
	switch severity {
	case "critical":
		return 9.5
	case "high":
		return 7.5
	case "medium":
		return 5.5
	case "low":
		return 3.0
	default:
		return 1.0
	}
}

// updateStats 更新统计信息
func (e *IntelligentWebEngine) updateStats(plan *intelligence.DetectionPlan, vulnCount int, duration time.Duration) {
	e.stats.SuccessfulScans++
	e.stats.TechnologiesFound += len(plan.Technologies)
	e.stats.VulnerabilitiesFound += vulnCount

	// 计算检测数量减少比例 (相比全量检测)
	totalPossibleTests := e.calculateTotalPossibleTests()
	actualTests := plan.TotalTests
	reduction := float64(totalPossibleTests-actualTests) / float64(totalPossibleTests) * 100

	// 更新平均减少比例
	e.stats.AverageReduction = (e.stats.AverageReduction*float64(e.stats.SuccessfulScans-1) + reduction) / float64(e.stats.SuccessfulScans)

	// 更新平均扫描时间
	e.stats.AverageScanTime = (e.stats.AverageScanTime*time.Duration(e.stats.SuccessfulScans-1) + duration) / time.Duration(e.stats.SuccessfulScans)
}

// calculateTotalPossibleTests 计算全量检测的总测试数
func (e *IntelligentWebEngine) calculateTotalPossibleTests() int {
	// 假设全量检测包括所有检测器的所有载荷
	return 50 // 示例值，实际应该根据所有检测器的载荷数量计算
}

// registerDetectors 注册检测器
func (e *IntelligentWebEngine) registerDetectors() {
	// 这里应该注册实际的检测器实现
	// e.detectors["sql_injection_detector"] = NewSQLInjectionDetector()
	// e.detectors["xss_detector"] = NewXSSDetector()
	// e.detectors["lfi_detector"] = NewLFIDetector()

	logger.Infof("已注册 %d 个检测器", len(e.detectors))
}

// GetName 获取引擎名称
func (e *IntelligentWebEngine) GetName() string {
	return e.name
}

// GetType 获取引擎类型
func (e *IntelligentWebEngine) GetType() string {
	return "intelligent_web"
}

// IsEnabled 检查引擎是否启用
func (e *IntelligentWebEngine) IsEnabled() bool {
	return e.enabled
}

// GetStats 获取统计信息
func (e *IntelligentWebEngine) GetStats() *IntelligentEngineStats {
	return e.stats
}

// GetSupportedVulnTypes 获取支持的漏洞类型
func (e *IntelligentWebEngine) GetSupportedVulnTypes() []string {
	return []string{
		"sql_injection",
		"xss",
		"file_inclusion",
		"code_injection",
		"privilege_escalation",
		"viewstate_manipulation",
		"directory_traversal",
		"command_injection",
	}
}

// OptimizeDetectionRules 优化检测规则
func (e *IntelligentWebEngine) OptimizeDetectionRules(feedback map[string]interface{}) error {
	// 基于用户反馈优化检测规则
	// 这里可以实现机器学习算法来优化规则选择
	logger.Infof("基于反馈优化检测规则")
	return nil
}

// GetDetectionEfficiency 获取检测效率报告
func (e *IntelligentWebEngine) GetDetectionEfficiency() map[string]interface{} {
	return map[string]interface{}{
		"total_scans":           e.stats.TotalScans,
		"successful_scans":      e.stats.SuccessfulScans,
		"average_reduction":     fmt.Sprintf("%.1f%%", e.stats.AverageReduction),
		"average_scan_time":     e.stats.AverageScanTime.String(),
		"technologies_per_scan": float64(e.stats.TechnologiesFound) / float64(e.stats.SuccessfulScans),
		"vulns_per_scan":        float64(e.stats.VulnerabilitiesFound) / float64(e.stats.SuccessfulScans),
	}
}
