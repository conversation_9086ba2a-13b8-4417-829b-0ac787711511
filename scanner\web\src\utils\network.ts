/**
 * 网络连接工具
 * 支持IPv4和IPv6双栈连接检测和自动切换
 */

export interface NetworkConfig {
  ipv4Endpoint: string
  ipv6Endpoint: string
  timeout: number
  retryCount: number
}

export interface ConnectionResult {
  success: boolean
  endpoint: string
  protocol: 'ipv4' | 'ipv6'
  responseTime: number
  error?: string
}

/**
 * 默认网络配置
 */
export const defaultNetworkConfig: NetworkConfig = {
  ipv4Endpoint: 'http://127.0.0.1:8082',
  ipv6Endpoint: 'http://[::1]:8082',
  timeout: 5000,
  retryCount: 2
}

/**
 * 检测单个端点的连通性
 */
async function testEndpoint(
  endpoint: string, 
  protocol: 'ipv4' | 'ipv6',
  timeout: number = 5000
): Promise<ConnectionResult> {
  const startTime = Date.now()
  
  try {
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), timeout)
    
    const response = await fetch(`${endpoint}/health`, {
      method: 'GET',
      signal: controller.signal,
      headers: {
        'Content-Type': 'application/json'
      }
    })
    
    clearTimeout(timeoutId)
    const responseTime = Date.now() - startTime
    
    if (response.ok) {
      return {
        success: true,
        endpoint,
        protocol,
        responseTime
      }
    } else {
      return {
        success: false,
        endpoint,
        protocol,
        responseTime,
        error: `HTTP ${response.status}: ${response.statusText}`
      }
    }
  } catch (error: any) {
    const responseTime = Date.now() - startTime
    return {
      success: false,
      endpoint,
      protocol,
      responseTime,
      error: error.message || '连接失败'
    }
  }
}

/**
 * 检测最佳连接端点
 */
export async function detectBestEndpoint(config: NetworkConfig = defaultNetworkConfig): Promise<ConnectionResult> {
  console.log('开始网络连接检测...')
  
  // 并行测试IPv4和IPv6
  const tests = [
    testEndpoint(config.ipv4Endpoint, 'ipv4', config.timeout),
    testEndpoint(config.ipv6Endpoint, 'ipv6', config.timeout)
  ]
  
  try {
    const results = await Promise.allSettled(tests)
    
    // 收集成功的连接
    const successfulConnections: ConnectionResult[] = []
    
    for (const result of results) {
      if (result.status === 'fulfilled' && result.value.success) {
        successfulConnections.push(result.value)
        console.log(`${result.value.protocol.toUpperCase()}连接成功: ${result.value.endpoint} (${result.value.responseTime}ms)`)
      } else if (result.status === 'fulfilled') {
        console.warn(`${result.value.protocol.toUpperCase()}连接失败: ${result.value.error}`)
      } else {
        console.error('连接测试异常:', result.reason)
      }
    }
    
    if (successfulConnections.length === 0) {
      throw new Error('所有连接端点都不可用')
    }
    
    // 选择响应时间最短的连接
    const bestConnection = successfulConnections.reduce((best, current) => 
      current.responseTime < best.responseTime ? current : best
    )
    
    console.log(`选择最佳连接: ${bestConnection.protocol.toUpperCase()} - ${bestConnection.endpoint}`)
    return bestConnection
    
  } catch (error) {
    console.error('网络检测失败:', error)
    
    // 返回默认的IPv4连接作为后备
    return {
      success: false,
      endpoint: config.ipv4Endpoint,
      protocol: 'ipv4',
      responseTime: 0,
      error: error instanceof Error ? error.message : '未知错误'
    }
  }
}

/**
 * 带重试的连接检测
 */
export async function detectBestEndpointWithRetry(config: NetworkConfig = defaultNetworkConfig): Promise<ConnectionResult> {
  let lastError: string = ''
  
  for (let attempt = 1; attempt <= config.retryCount; attempt++) {
    try {
      console.log(`网络检测尝试 ${attempt}/${config.retryCount}`)
      const result = await detectBestEndpoint(config)
      
      if (result.success) {
        return result
      }
      
      lastError = result.error || '连接失败'
      
      if (attempt < config.retryCount) {
        const delay = Math.min(1000 * attempt, 5000) // 指数退避，最大5秒
        console.log(`等待 ${delay}ms 后重试...`)
        await new Promise(resolve => setTimeout(resolve, delay))
      }
      
    } catch (error) {
      lastError = error instanceof Error ? error.message : '未知错误'
      console.error(`检测尝试 ${attempt} 失败:`, error)
      
      if (attempt < config.retryCount) {
        const delay = Math.min(1000 * attempt, 5000)
        await new Promise(resolve => setTimeout(resolve, delay))
      }
    }
  }
  
  // 所有重试都失败，返回失败结果
  return {
    success: false,
    endpoint: config.ipv4Endpoint,
    protocol: 'ipv4',
    responseTime: 0,
    error: `重试 ${config.retryCount} 次后仍然失败: ${lastError}`
  }
}

/**
 * 获取当前最佳API基础URL
 */
let cachedEndpoint: string | null = null
let lastDetectionTime: number = 0
const CACHE_DURATION = 5 * 60 * 1000 // 5分钟缓存

export async function getBestApiBaseUrl(): Promise<string> {
  const now = Date.now()
  
  // 如果有缓存且未过期，直接返回
  if (cachedEndpoint && (now - lastDetectionTime) < CACHE_DURATION) {
    return cachedEndpoint
  }
  
  try {
    const result = await detectBestEndpointWithRetry()
    
    if (result.success) {
      cachedEndpoint = result.endpoint
      lastDetectionTime = now
      return result.endpoint
    } else {
      console.warn('网络检测失败，使用默认端点:', result.error)
      return defaultNetworkConfig.ipv4Endpoint
    }
  } catch (error) {
    console.error('获取最佳API端点失败:', error)
    return defaultNetworkConfig.ipv4Endpoint
  }
}

/**
 * 清除端点缓存
 */
export function clearEndpointCache(): void {
  cachedEndpoint = null
  lastDetectionTime = 0
  console.log('端点缓存已清除')
}

/**
 * 检查IPv6支持
 */
export async function checkIPv6Support(): Promise<boolean> {
  try {
    const result = await testEndpoint(defaultNetworkConfig.ipv6Endpoint, 'ipv6', 3000)
    return result.success
  } catch {
    return false
  }
}

/**
 * 获取网络状态信息
 */
export async function getNetworkStatus(): Promise<{
  ipv4Available: boolean
  ipv6Available: boolean
  bestEndpoint: string
  bestProtocol: 'ipv4' | 'ipv6'
  responseTime: number
}> {
  const [ipv4Result, ipv6Result] = await Promise.allSettled([
    testEndpoint(defaultNetworkConfig.ipv4Endpoint, 'ipv4'),
    testEndpoint(defaultNetworkConfig.ipv6Endpoint, 'ipv6')
  ])
  
  const ipv4Available = ipv4Result.status === 'fulfilled' && ipv4Result.value.success
  const ipv6Available = ipv6Result.status === 'fulfilled' && ipv6Result.value.success
  
  let bestEndpoint = defaultNetworkConfig.ipv4Endpoint
  let bestProtocol: 'ipv4' | 'ipv6' = 'ipv4'
  let responseTime = 0
  
  if (ipv4Available && ipv6Available) {
    const ipv4Time = ipv4Result.status === 'fulfilled' ? ipv4Result.value.responseTime : Infinity
    const ipv6Time = ipv6Result.status === 'fulfilled' ? ipv6Result.value.responseTime : Infinity
    
    if (ipv6Time < ipv4Time) {
      bestEndpoint = defaultNetworkConfig.ipv6Endpoint
      bestProtocol = 'ipv6'
      responseTime = ipv6Time
    } else {
      responseTime = ipv4Time
    }
  } else if (ipv6Available) {
    bestEndpoint = defaultNetworkConfig.ipv6Endpoint
    bestProtocol = 'ipv6'
    responseTime = ipv6Result.status === 'fulfilled' ? ipv6Result.value.responseTime : 0
  } else if (ipv4Available) {
    responseTime = ipv4Result.status === 'fulfilled' ? ipv4Result.value.responseTime : 0
  }
  
  return {
    ipv4Available,
    ipv6Available,
    bestEndpoint,
    bestProtocol,
    responseTime
  }
}
