package vulnerability

import (
	"fmt"
	"net"
	"net/url"
	"strconv"
	"time"

	"scanner/internal/models"
	"scanner/pkg/logger"

	"gorm.io/gorm"
)

// Service 漏洞服务
type Service struct {
	db *gorm.DB
}

// NewService 创建漏洞服务
func NewService(db *gorm.DB) *Service {
	return &Service{
		db: db,
	}
}

// GetVulnerabilities 获取漏洞列表
func (s *Service) GetVulnerabilities(page, pageSize int, conditions map[string]interface{}, search string) ([]models.Vulnerability, int64, error) {
	var vulnerabilities []models.Vulnerability
	var total int64

	// 构建查询
	query := s.db.Model(&models.Vulnerability{})

	// 添加条件
	for key, value := range conditions {
		query = query.Where(fmt.Sprintf("%s = ?", key), value)
	}

	// 添加搜索条件
	if search != "" {
		query = query.Where("name LIKE ? OR description LIKE ? OR cve LIKE ?",
			"%"+search+"%", "%"+search+"%", "%"+search+"%")
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("获取漏洞总数失败: %w", err)
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Preload("Asset").Preload("Task").
		Offset(offset).Limit(pageSize).
		Order("created_at DESC").
		Find(&vulnerabilities).Error; err != nil {
		return nil, 0, fmt.Errorf("获取漏洞列表失败: %w", err)
	}

	return vulnerabilities, total, nil
}

// GetVulnerabilityByID 根据ID获取漏洞详情
func (s *Service) GetVulnerabilityByID(id uint) (*models.Vulnerability, error) {
	var vulnerability models.Vulnerability

	if err := s.db.Preload("Asset").Preload("Task").
		First(&vulnerability, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("获取漏洞详情失败: %w", err)
	}

	return &vulnerability, nil
}

// CreateVulnerability 创建漏洞
func (s *Service) CreateVulnerability(vulnerability *models.Vulnerability) error {
	// 如果漏洞没有关联资产，尝试从扫描任务中获取资产关联
	if vulnerability.AssetID == nil && vulnerability.TaskID > 0 {
		var task models.ScanTask
		if err := s.db.First(&task, vulnerability.TaskID).Error; err == nil {
			if task.AssetID != nil {
				vulnerability.AssetID = task.AssetID
				logger.Infof("从扫描任务 %d 中获取资产关联: %d", vulnerability.TaskID, *task.AssetID)
			}
		}
	}

	// 如果仍然没有资产关联，尝试根据URL自动创建或关联资产
	if vulnerability.AssetID == nil && vulnerability.URL != "" {
		assetID := s.findOrCreateAssetFromURL(vulnerability.URL)
		if assetID > 0 {
			vulnerability.AssetID = &assetID
			logger.Infof("根据URL自动关联资产: %d", assetID)
		}
	}

	if err := s.db.Create(vulnerability).Error; err != nil {
		return fmt.Errorf("创建漏洞失败: %w", err)
	}

	logger.Infof("创建漏洞成功，ID: %d, 关联资产: %v", vulnerability.ID, vulnerability.AssetID)
	return nil
}

// CreateVulnerabilityFromData 从数据映射创建漏洞
func (s *Service) CreateVulnerabilityFromData(data map[string]interface{}) error {
	// 构建漏洞模型
	vulnerability := &models.Vulnerability{
		Name:        getString(data, "name"),
		Title:       getString(data, "name"), // 使用name作为title
		Description: getString(data, "description"),
		Severity:    getString(data, "severity"),
		URL:         getString(data, "target"),
		Evidence:    getString(data, "evidence"),
		Solution:    getString(data, "solution"),
		References:  getString(data, "references"),
		Status:      getString(data, "status"),
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	// 设置任务ID
	if taskID, ok := data["task_id"].(uint); ok {
		vulnerability.TaskID = taskID
	}

	// 设置置信度（如果有的话）
	if confidence, ok := data["confidence"].(float64); ok {
		// 可以将置信度存储在元数据中或其他字段
		_ = confidence // 暂时忽略
	}

	return s.CreateVulnerability(vulnerability)
}

// getString 从map中安全获取字符串值
func getString(data map[string]interface{}, key string) string {
	if value, ok := data[key].(string); ok {
		return value
	}
	return ""
}

// UpdateVulnerability 更新漏洞信息
func (s *Service) UpdateVulnerability(id uint, updateData map[string]interface{}) error {
	// 添加更新时间
	updateData["updated_at"] = time.Now()

	// 如果状态更新为已修复，记录修复时间
	if status, ok := updateData["status"]; ok && status == "fixed" {
		updateData["fixed_at"] = time.Now()
	}

	if err := s.db.Model(&models.Vulnerability{}).
		Where("id = ?", id).
		Updates(updateData).Error; err != nil {
		return fmt.Errorf("更新漏洞失败: %w", err)
	}

	logger.Infof("更新漏洞成功，ID: %d", id)
	return nil
}

// DeleteVulnerability 删除漏洞
func (s *Service) DeleteVulnerability(id uint) error {
	if err := s.db.Delete(&models.Vulnerability{}, id).Error; err != nil {
		return fmt.Errorf("删除漏洞失败: %w", err)
	}

	logger.Infof("删除漏洞成功，ID: %d", id)
	return nil
}

// findOrCreateAssetFromURL 根据URL查找或创建资产
func (s *Service) findOrCreateAssetFromURL(urlStr string) uint {
	// 解析URL
	u, err := url.Parse(urlStr)
	if err != nil {
		logger.Errorf("解析URL失败: %v", err)
		return 0
	}

	hostname := u.Hostname()
	port := u.Port()
	scheme := u.Scheme

	// 默认端口处理
	if port == "" {
		switch scheme {
		case "http":
			port = "80"
		case "https":
			port = "443"
		default:
			port = "80"
		}
	}

	// 查找现有资产
	var asset models.Asset
	query := s.db.Where("domain = ? OR ip = ?", hostname, hostname)
	if port != "" {
		portInt, _ := strconv.Atoi(port)
		query = query.Where("port = ?", portInt)
	}

	if err := query.First(&asset).Error; err == nil {
		// 找到现有资产
		return asset.ID
	}

	// 创建新资产
	portInt, _ := strconv.Atoi(port)
	newAsset := &models.Asset{
		Name:     fmt.Sprintf("自动发现-%s", hostname),
		Type:     "web",
		Domain:   hostname,
		Port:     portInt,
		Protocol: scheme,
		Status:   "active",
	}

	// 尝试判断是IP还是域名
	if net.ParseIP(hostname) != nil {
		newAsset.IP = hostname
		newAsset.Domain = ""
	}

	if err := s.db.Create(newAsset).Error; err != nil {
		logger.Errorf("创建资产失败: %v", err)
		return 0
	}

	logger.Infof("自动创建资产成功: ID=%d, %s:%d", newAsset.ID, hostname, portInt)
	return newAsset.ID
}

// BatchUpdateVulnerabilities 批量更新漏洞状态
func (s *Service) BatchUpdateVulnerabilities(ids []uint, status string) error {
	updateData := map[string]interface{}{
		"status":     status,
		"updated_at": time.Now(),
	}

	// 如果状态更新为已修复，记录修复时间
	if status == "fixed" {
		updateData["fixed_at"] = time.Now()
	}

	if err := s.db.Model(&models.Vulnerability{}).
		Where("id IN ?", ids).
		Updates(updateData).Error; err != nil {
		return fmt.Errorf("批量更新漏洞状态失败: %w", err)
	}

	logger.Infof("批量更新漏洞状态成功，数量: %d", len(ids))
	return nil
}

// GetVulnerabilityStats 获取漏洞统计信息
func (s *Service) GetVulnerabilityStats() (map[string]interface{}, error) {
	stats := map[string]interface{}{}

	// 总数统计
	var total int64
	if err := s.db.Model(&models.Vulnerability{}).Count(&total).Error; err != nil {
		return nil, fmt.Errorf("获取漏洞总数失败: %w", err)
	}
	stats["total"] = total

	// 按严重程度统计
	severityStats := []struct {
		Severity string `json:"severity"`
		Count    int64  `json:"count"`
	}{}

	if err := s.db.Model(&models.Vulnerability{}).
		Select("severity, COUNT(*) as count").
		Group("severity").
		Find(&severityStats).Error; err != nil {
		return nil, fmt.Errorf("获取严重程度统计失败: %w", err)
	}

	// 初始化各严重程度计数
	stats["critical"] = int64(0)
	stats["high"] = int64(0)
	stats["medium"] = int64(0)
	stats["low"] = int64(0)
	stats["info"] = int64(0)

	for _, stat := range severityStats {
		stats[stat.Severity] = stat.Count
	}

	// 按状态统计
	statusStats := []struct {
		Status string `json:"status"`
		Count  int64  `json:"count"`
	}{}

	if err := s.db.Model(&models.Vulnerability{}).
		Select("status, COUNT(*) as count").
		Group("status").
		Find(&statusStats).Error; err != nil {
		return nil, fmt.Errorf("获取状态统计失败: %w", err)
	}

	// 初始化各状态计数
	stats["open"] = int64(0)
	stats["fixed"] = int64(0)
	stats["ignored"] = int64(0)

	for _, stat := range statusStats {
		stats[stat.Status] = stat.Count
	}

	return stats, nil
}

// GetVulnerabilitiesByAssetID 根据资产ID获取漏洞列表
func (s *Service) GetVulnerabilitiesByAssetID(assetID uint) ([]models.Vulnerability, error) {
	var vulnerabilities []models.Vulnerability

	if err := s.db.Where("asset_id = ?", assetID).
		Order("severity DESC, created_at DESC").
		Find(&vulnerabilities).Error; err != nil {
		return nil, fmt.Errorf("获取资产漏洞列表失败: %w", err)
	}

	return vulnerabilities, nil
}

// GetVulnerabilitiesByTaskID 根据任务ID获取漏洞列表
func (s *Service) GetVulnerabilitiesByTaskID(taskID uint) ([]models.Vulnerability, error) {
	var vulnerabilities []models.Vulnerability

	if err := s.db.Where("task_id = ?", taskID).
		Order("severity DESC, created_at DESC").
		Find(&vulnerabilities).Error; err != nil {
		return nil, fmt.Errorf("获取任务漏洞列表失败: %w", err)
	}

	return vulnerabilities, nil
}

// BatchCreateVulnerabilities 批量创建漏洞
func (s *Service) BatchCreateVulnerabilities(vulnerabilities []models.Vulnerability) error {
	if len(vulnerabilities) == 0 {
		return nil
	}

	if err := s.db.CreateInBatches(vulnerabilities, 100).Error; err != nil {
		return fmt.Errorf("批量创建漏洞失败: %w", err)
	}

	logger.Infof("批量创建漏洞成功，数量: %d", len(vulnerabilities))
	return nil
}

// UpdateTaskInfoGatheringData 更新扫描任务的信息收集数据
func (s *Service) UpdateTaskInfoGatheringData(taskID string, infoData string) error {
	// 将taskID转换为uint
	id, err := strconv.ParseUint(taskID, 10, 32)
	if err != nil {
		return fmt.Errorf("无效的任务ID: %v", err)
	}

	// 更新扫描任务的信息收集数据字段
	if err := s.db.Model(&models.ScanTask{}).
		Where("id = ?", uint(id)).
		Update("info_gathering_data", infoData).Error; err != nil {
		return fmt.Errorf("更新信息收集数据失败: %w", err)
	}

	logger.Infof("更新任务 %s 的信息收集数据成功", taskID)
	return nil
}
