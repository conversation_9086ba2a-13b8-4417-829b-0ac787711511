package rules

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"path/filepath"
	"regexp"
	"strings"
	"time"

	"scanner/internal/scanner/types"
	"scanner/pkg/logger"
)

// RuleEngine 扫描规则引擎
type RuleEngine struct {
	rules []ScanRule
}

// ScanRule 扫描规则结构
type ScanRule struct {
	ID          string            `json:"id"`          // 规则ID
	Name        string            `json:"name"`        // 规则名称
	Description string            `json:"description"` // 规则描述
	Category    string            `json:"category"`    // 规则分类
	Severity    string            `json:"severity"`    // 严重程度
	Type        string            `json:"type"`        // 规则类型: web, network, file
	Enabled     bool              `json:"enabled"`     // 是否启用
	Conditions  []RuleCondition   `json:"conditions"`  // 匹配条件
	Payloads    []string          `json:"payloads"`    // 测试载荷
	Solution    string            `json:"solution"`    // 解决方案
	References  []string          `json:"references"`  // 参考链接
	Tags        []string          `json:"tags"`        // 标签
	Metadata    map[string]string `json:"metadata"`    // 元数据
	CreatedAt   time.Time         `json:"created_at"`  // 创建时间
	UpdatedAt   time.Time         `json:"updated_at"`  // 更新时间
}

// RuleCondition 规则条件
type RuleCondition struct {
	Type          string `json:"type"`           // 条件类型: response, header, status, banner
	Field         string `json:"field"`          // 字段名
	Operator      string `json:"operator"`       // 操作符: contains, equals, regex, not_contains
	Value         string `json:"value"`          // 匹配值
	CaseSensitive bool   `json:"case_sensitive"` // 是否区分大小写
}

// RuleMatch 规则匹配结果
type RuleMatch struct {
	Rule     *ScanRule `json:"rule"`
	Matched  bool      `json:"matched"`
	Evidence string    `json:"evidence"`
	Details  string    `json:"details"`
}

// NewRuleEngine 创建规则引擎
func NewRuleEngine() *RuleEngine {
	return &RuleEngine{
		rules: make([]ScanRule, 0),
	}
}

// LoadRulesFromDirectory 从目录加载规则
func (re *RuleEngine) LoadRulesFromDirectory(rulesDir string) error {
	logger.Infof("开始从目录加载扫描规则: %s", rulesDir)

	// 读取规则目录下的所有JSON文件
	files, err := filepath.Glob(filepath.Join(rulesDir, "*.json"))
	if err != nil {
		return fmt.Errorf("读取规则目录失败: %v", err)
	}

	loadedCount := 0
	for _, file := range files {
		if err := re.LoadRuleFromFile(file); err != nil {
			logger.Errorf("加载规则文件失败 %s: %v", file, err)
			continue
		}
		loadedCount++
	}

	logger.Infof("成功加载 %d 个规则文件，总计 %d 条规则", loadedCount, len(re.rules))
	return nil
}

// LoadRuleFromFile 从文件加载规则
func (re *RuleEngine) LoadRuleFromFile(filename string) error {
	data, err := ioutil.ReadFile(filename)
	if err != nil {
		return fmt.Errorf("读取规则文件失败: %v", err)
	}

	var rule ScanRule
	if err := json.Unmarshal(data, &rule); err != nil {
		return fmt.Errorf("解析规则文件失败: %v", err)
	}

	// 验证规则
	if err := re.validateRule(&rule); err != nil {
		return fmt.Errorf("规则验证失败: %v", err)
	}

	re.rules = append(re.rules, rule)
	logger.Debugf("成功加载规则: %s - %s", rule.ID, rule.Name)
	return nil
}

// validateRule 验证规则
func (re *RuleEngine) validateRule(rule *ScanRule) error {
	if rule.ID == "" {
		return fmt.Errorf("规则ID不能为空")
	}
	if rule.Name == "" {
		return fmt.Errorf("规则名称不能为空")
	}
	if rule.Type == "" {
		return fmt.Errorf("规则类型不能为空")
	}
	if len(rule.Conditions) == 0 {
		return fmt.Errorf("规则条件不能为空")
	}

	// 检查规则ID是否重复
	for _, existingRule := range re.rules {
		if existingRule.ID == rule.ID {
			return fmt.Errorf("规则ID重复: %s", rule.ID)
		}
	}

	return nil
}

// GetRulesByType 根据类型获取规则
func (re *RuleEngine) GetRulesByType(ruleType string) []ScanRule {
	var rules []ScanRule
	for _, rule := range re.rules {
		if rule.Type == ruleType && rule.Enabled {
			rules = append(rules, rule)
		}
	}
	return rules
}

// GetRulesByCategory 根据分类获取规则
func (re *RuleEngine) GetRulesByCategory(category string) []ScanRule {
	var rules []ScanRule
	for _, rule := range re.rules {
		if rule.Category == category && rule.Enabled {
			rules = append(rules, rule)
		}
	}
	return rules
}

// MatchRule 匹配规则
func (re *RuleEngine) MatchRule(rule *ScanRule, target string, response string, headers map[string]string, statusCode int, banner string) *RuleMatch {
	match := &RuleMatch{
		Rule:    rule,
		Matched: false,
	}

	// 检查所有条件
	allConditionsMet := true
	var evidences []string

	for _, condition := range rule.Conditions {
		conditionMet, evidence := re.evaluateCondition(&condition, response, headers, statusCode, banner)
		if !conditionMet {
			allConditionsMet = false
			break
		}
		if evidence != "" {
			evidences = append(evidences, evidence)
		}
	}

	if allConditionsMet {
		match.Matched = true
		match.Evidence = strings.Join(evidences, "; ")
		match.Details = fmt.Sprintf("规则 %s 匹配成功", rule.Name)
	}

	return match
}

// evaluateCondition 评估条件
func (re *RuleEngine) evaluateCondition(condition *RuleCondition, response string, headers map[string]string, statusCode int, banner string) (bool, string) {
	var targetValue string
	var evidence string

	// 根据条件类型获取目标值
	switch condition.Type {
	case "response":
		targetValue = response
	case "header":
		if condition.Field != "" {
			targetValue = headers[condition.Field]
		}
	case "status":
		targetValue = fmt.Sprintf("%d", statusCode)
	case "banner":
		targetValue = banner
	default:
		return false, ""
	}

	// 处理大小写敏感性
	compareValue := condition.Value
	if !condition.CaseSensitive {
		targetValue = strings.ToLower(targetValue)
		compareValue = strings.ToLower(compareValue)
	}

	// 根据操作符进行匹配
	switch condition.Operator {
	case "contains":
		if strings.Contains(targetValue, compareValue) {
			evidence = fmt.Sprintf("%s包含: %s", condition.Type, condition.Value)
			return true, evidence
		}
	case "equals":
		if targetValue == compareValue {
			evidence = fmt.Sprintf("%s等于: %s", condition.Type, condition.Value)
			return true, evidence
		}
	case "not_contains":
		if !strings.Contains(targetValue, compareValue) {
			evidence = fmt.Sprintf("%s不包含: %s", condition.Type, condition.Value)
			return true, evidence
		}
	case "regex":
		matched, err := regexp.MatchString(compareValue, targetValue)
		if err == nil && matched {
			evidence = fmt.Sprintf("%s匹配正则: %s", condition.Type, condition.Value)
			return true, evidence
		}
	}

	return false, ""
}

// GetRulesCount 获取规则数量
func (re *RuleEngine) GetRulesCount() int {
	return len(re.rules)
}

// GetEnabledRulesCount 获取启用的规则数量
func (re *RuleEngine) GetEnabledRulesCount() int {
	count := 0
	for _, rule := range re.rules {
		if rule.Enabled {
			count++
		}
	}
	return count
}

// GetRulesByTags 根据标签获取规则
func (re *RuleEngine) GetRulesByTags(tags []string) []ScanRule {
	var rules []ScanRule
	for _, rule := range re.rules {
		if rule.Enabled && re.hasAnyTag(rule.Tags, tags) {
			rules = append(rules, rule)
		}
	}
	return rules
}

// hasAnyTag 检查是否包含任意标签
func (re *RuleEngine) hasAnyTag(ruleTags []string, searchTags []string) bool {
	for _, searchTag := range searchTags {
		for _, ruleTag := range ruleTags {
			if strings.EqualFold(ruleTag, searchTag) {
				return true
			}
		}
	}
	return false
}

// CreateVulnerabilityFromMatch 从匹配结果创建漏洞
func (re *RuleEngine) CreateVulnerabilityFromMatch(match *RuleMatch, target string, payload string) *types.Vulnerability {
	if !match.Matched {
		return nil
	}

	rule := match.Rule
	return &types.Vulnerability{
		ID:          fmt.Sprintf("rule_%s_%d", rule.ID, time.Now().UnixNano()),
		Type:        rule.Category,
		Name:        rule.Name,
		Description: rule.Description,
		Severity:    rule.Severity,
		URL:         target,
		Payload:     payload,
		Evidence:    match.Evidence,
		Solution:    rule.Solution,
		CreatedAt:   time.Now(),
	}
}
