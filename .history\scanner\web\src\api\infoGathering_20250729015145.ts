// 信息收集相关API接口

import { ApiClient } from '@/api/client'
import type { 
  InfoGatheringLog, 
  InfoGatheringStats, 
  InfoGatheringLogsResponse 
} from '@/types/infoGathering'

/**
 * 信息收集API接口
 */
export const infoGatheringApi = {
  /**
   * 获取信息收集日志
   * @param taskId 任务ID
   * @param params 查询参数
   */
  getLogs(taskId: number, params?: {
    info_type?: string
    page?: number
    size?: number
  }): Promise<InfoGatheringLogsResponse> {
    return ApiClient.get(`/info-gathering/logs/${taskId}`, { params })
  },

  /**
   * 获取指纹识别信息
   * @param taskId 任务ID
   */
  getFingerprints(taskId: number): Promise<{ data: InfoGatheringLog[] }> {
    return ApiClient.get(`/info-gathering/fingerprints/${taskId}`)
  },

  /**
   * 获取目录发现信息
   * @param taskId 任务ID
   */
  getDirectories(taskId: number): Promise<{ data: InfoGatheringLog[] }> {
    return ApiClient.get(`/info-gathering/directories/${taskId}`)
  },

  /**
   * 获取服务识别信息
   * @param taskId 任务ID
   */
  getServices(taskId: number): Promise<{ data: InfoGatheringLog[] }> {
    return ApiClient.get(`/info-gathering/services/${taskId}`)
  },

  /**
   * 获取技术栈信息
   * @param taskId 任务ID
   */
  getTechnologies(taskId: number): Promise<{ data: InfoGatheringLog[] }> {
    return ApiClient.get(`/info-gathering/technologies/${taskId}`)
  },

  /**
   * 获取信息收集统计
   * @param taskId 任务ID
   */
  getStats(taskId: number): Promise<{ data: InfoGatheringStats }> {
    return ApiClient.get(`/info-gathering/stats/${taskId}`)
  },

  /**
   * 删除信息收集日志
   * @param taskId 任务ID
   */
  deleteLogs(taskId: number): Promise<{ message: string }> {
    return ApiClient.delete(`/info-gathering/logs/${taskId}`)
  }
}
