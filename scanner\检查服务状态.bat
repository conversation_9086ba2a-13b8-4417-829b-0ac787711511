@echo off
chcp 65001 >nul
title 漏洞扫描器服务状态检查

echo =================================
echo     漏洞扫描器服务状态检查
echo =================================
echo.

echo 正在检查后端服务状态...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8082/health' -UseBasicParsing -TimeoutSec 5; if ($response.StatusCode -eq 200) { Write-Host '✅ 后端服务运行正常 (端口: 8082)' -ForegroundColor Green; $content = $response.Content | ConvertFrom-Json; Write-Host '   版本: ' $content.version -ForegroundColor Cyan; Write-Host '   状态: ' $content.status -ForegroundColor Cyan } else { Write-Host '❌ 后端服务异常' -ForegroundColor Red } } catch { Write-Host '❌ 后端服务未启动或无法访问' -ForegroundColor Red }"

echo.
echo 正在检查前端服务状态...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:3000' -UseBasicParsing -TimeoutSec 5; if ($response.StatusCode -eq 200) { Write-Host '✅ 前端服务运行正常 (端口: 3000)' -ForegroundColor Green } else { Write-Host '❌ 前端服务异常' -ForegroundColor Red } } catch { Write-Host '❌ 前端服务未启动或无法访问' -ForegroundColor Red }"

echo.
echo 正在检查数据库连接...
if exist "data\scanner.db" (
    echo ✅ 数据库文件存在
) else (
    echo ❌ 数据库文件不存在
)

echo.
echo =================================
echo     服务访问地址
echo =================================
echo 前端应用: http://localhost:3000
echo 后端API:  http://localhost:8082
echo 健康检查: http://localhost:8082/health
echo API文档:  http://localhost:8082/swagger/index.html
echo.

echo 按任意键退出...
pause >nul
