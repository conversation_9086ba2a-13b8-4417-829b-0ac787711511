# 漏洞扫描器服务状态检查脚本
Write-Host "=================================" -ForegroundColor Yellow
Write-Host "     漏洞扫描器服务状态检查" -ForegroundColor Yellow
Write-Host "=================================" -ForegroundColor Yellow
Write-Host ""

# 检查后端服务
Write-Host "正在检查后端服务状态..." -ForegroundColor Cyan
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8082/health" -UseBasicParsing -TimeoutSec 5
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ 后端服务运行正常 (端口: 8082)" -ForegroundColor Green
        $content = $response.Content | ConvertFrom-Json
        Write-Host "   版本: $($content.version)" -ForegroundColor Cyan
        Write-Host "   状态: $($content.status)" -ForegroundColor Cyan
    } else {
        Write-Host "❌ 后端服务异常" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ 后端服务未启动或无法访问" -ForegroundColor Red
}

Write-Host ""

# 检查前端服务
Write-Host "正在检查前端服务状态..." -ForegroundColor Cyan
try {
    $response = Invoke-WebRequest -Uri "http://localhost:3000" -UseBasicParsing -TimeoutSec 5
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ 前端服务运行正常 (端口: 3000)" -ForegroundColor Green
    } else {
        Write-Host "❌ 前端服务异常" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ 前端服务未启动或无法访问" -ForegroundColor Red
}

Write-Host ""

# 检查数据库文件
Write-Host "正在检查数据库..." -ForegroundColor Cyan
if (Test-Path "data\scanner.db") {
    Write-Host "✅ 数据库文件存在" -ForegroundColor Green
} else {
    Write-Host "❌ 数据库文件不存在" -ForegroundColor Red
}

Write-Host ""
Write-Host "=================================" -ForegroundColor Yellow
Write-Host "     服务访问地址" -ForegroundColor Yellow
Write-Host "=================================" -ForegroundColor Yellow
Write-Host "前端应用: http://localhost:3000" -ForegroundColor White
Write-Host "后端API:  http://localhost:8082" -ForegroundColor White
Write-Host "健康检查: http://localhost:8082/health" -ForegroundColor White
Write-Host "API文档:  http://localhost:8082/swagger/index.html" -ForegroundColor White
Write-Host ""

# 检查进程
Write-Host "正在检查相关进程..." -ForegroundColor Cyan
$goProcess = Get-Process | Where-Object { $_.ProcessName -like "*go*" -or $_.CommandLine -like "*main.go*" } -ErrorAction SilentlyContinue
$nodeProcess = Get-Process | Where-Object { $_.ProcessName -eq "node" } -ErrorAction SilentlyContinue

if ($goProcess) {
    Write-Host "✅ Go后端进程运行中" -ForegroundColor Green
} else {
    Write-Host "❌ 未找到Go后端进程" -ForegroundColor Red
}

if ($nodeProcess) {
    Write-Host "✅ Node.js前端进程运行中" -ForegroundColor Green
} else {
    Write-Host "❌ 未找到Node.js前端进程" -ForegroundColor Red
}

Write-Host ""
Write-Host "按任意键退出..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
