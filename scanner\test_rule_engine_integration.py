#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试规则引擎集成状态
"""

import requests
import json
import time

def test_backend_health():
    """测试后端健康状态"""
    print("=== 测试后端健康状态 ===")
    try:
        response = requests.get("http://localhost:8082/health", timeout=5)
        if response.status_code == 200:
            health_data = response.json()
            print(f"✅ 后端服务正常运行")
            print(f"  版本: {health_data.get('version', 'unknown')}")
            print(f"  状态: {health_data.get('status', 'unknown')}")
            return True
        else:
            print(f"❌ 后端服务状态异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接后端服务: {e}")
        return False

def login_and_get_token():
    """登录并获取token"""
    print("\n=== 登录获取认证token ===")
    try:
        login_data = {"username": "admin", "password": "admin123"}
        response = requests.post("http://localhost:8082/api/v1/auth/login", json=login_data, timeout=5)
        
        if response.status_code == 200:
            token = response.json().get('data', {}).get('token')
            print("✅ 成功获取认证token")
            return token
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 登录请求失败: {e}")
        return None

def test_web_scan_with_rules(token):
    """测试Web扫描中的规则匹配"""
    print("\n=== 测试Web扫描中的规则匹配 ===")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # 创建Web扫描任务
    scan_data = {
        "name": "规则引擎测试扫描",
        "type": "web",
        "targets": ["http://testphp.vulnweb.com"],
        "config": {
            "scan_type": "web",
            "enable_crawler": True,
            "max_depth": 2,
            "timeout": 30
        }
    }
    
    try:
        # 创建扫描任务
        response = requests.post("http://localhost:8082/api/v1/scans", 
                               json=scan_data, headers=headers, timeout=10)
        
        if response.status_code == 200:
            task_data = response.json().get('data', {})
            task_id = task_data.get('id')
            print(f"✅ 创建扫描任务成功，任务ID: {task_id}")
            
            # 启动扫描
            start_response = requests.post(f"http://localhost:8082/api/v1/scans/{task_id}/start",
                                         headers=headers, timeout=10)
            
            if start_response.status_code == 200:
                print("✅ 扫描任务已启动")
                
                # 监控扫描进度
                monitor_scan_progress(task_id, headers)
                
                # 检查扫描结果中的规则匹配
                check_rule_matches(task_id, headers)
                
            else:
                print(f"❌ 启动扫描失败: {start_response.status_code}")
        else:
            print(f"❌ 创建扫描任务失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 扫描测试失败: {e}")

def monitor_scan_progress(task_id, headers):
    """监控扫描进度"""
    print(f"\n=== 监控扫描进度 (任务ID: {task_id}) ===")
    
    max_wait_time = 300  # 最大等待5分钟
    start_time = time.time()
    
    while time.time() - start_time < max_wait_time:
        try:
            response = requests.get(f"http://localhost:8082/api/v1/scans/{task_id}",
                                  headers=headers, timeout=5)
            
            if response.status_code == 200:
                task_data = response.json().get('data', {})
                status = task_data.get('status', 'unknown')
                progress = task_data.get('progress', 0)
                
                print(f"📊 扫描状态: {status}, 进度: {progress}%")
                
                if status in ['completed', 'failed', 'cancelled']:
                    print(f"✅ 扫描已完成，最终状态: {status}")
                    return status
                    
            time.sleep(10)  # 等待10秒后再次检查
            
        except Exception as e:
            print(f"❌ 获取扫描状态失败: {e}")
            break
    
    print("⚠️ 扫描监控超时")
    return "timeout"

def check_rule_matches(task_id, headers):
    """检查规则匹配结果"""
    print(f"\n=== 检查规则匹配结果 (任务ID: {task_id}) ===")
    
    try:
        # 获取扫描详情
        response = requests.get(f"http://localhost:8082/api/v1/scans/{task_id}",
                              headers=headers, timeout=5)
        
        if response.status_code == 200:
            task_data = response.json().get('data', {})
            print(f"📋 扫描任务详情:")
            print(f"  任务名称: {task_data.get('name', 'unknown')}")
            print(f"  扫描类型: {task_data.get('type', 'unknown')}")
            print(f"  扫描状态: {task_data.get('status', 'unknown')}")
            print(f"  扫描进度: {task_data.get('progress', 0)}%")
            
            # 获取漏洞列表
            vuln_response = requests.get(f"http://localhost:8082/api/v1/vulnerabilities?task_id={task_id}",
                                       headers=headers, timeout=5)
            
            if vuln_response.status_code == 200:
                vuln_data = vuln_response.json().get('data', {})
                vulnerabilities = vuln_data.get('vulnerabilities', [])
                
                print(f"\n🔍 发现的漏洞:")
                print(f"  总漏洞数: {len(vulnerabilities)}")
                
                rule_based_vulns = []
                for vuln in vulnerabilities:
                    if 'rule-based' in vuln.get('tags', []) or 'rule' in vuln.get('type', '').lower():
                        rule_based_vulns.append(vuln)
                
                print(f"  规则匹配漏洞: {len(rule_based_vulns)}")
                
                if rule_based_vulns:
                    print(f"\n📝 规则匹配漏洞详情:")
                    for i, vuln in enumerate(rule_based_vulns[:5], 1):  # 只显示前5个
                        print(f"  {i}. {vuln.get('name', 'unknown')}")
                        print(f"     严重程度: {vuln.get('severity', 'unknown')}")
                        print(f"     目标: {vuln.get('url', 'unknown')}")
                        print(f"     证据: {vuln.get('evidence', 'unknown')[:100]}...")
                        print()
                else:
                    print("  ⚠️ 未发现基于规则的漏洞")
                    
            else:
                print(f"❌ 获取漏洞列表失败: {vuln_response.status_code}")
        else:
            print(f"❌ 获取扫描详情失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 检查规则匹配结果失败: {e}")

def check_scan_logs(task_id, headers):
    """检查扫描日志中的规则相关信息"""
    print(f"\n=== 检查扫描日志 (任务ID: {task_id}) ===")
    
    try:
        response = requests.get(f"http://localhost:8082/api/v1/scans/{task_id}/logs",
                              headers=headers, timeout=5)
        
        if response.status_code == 200:
            logs_data = response.json().get('data', [])
            
            rule_logs = []
            for log in logs_data:
                message = log.get('message', '').lower()
                if '规则' in message or 'rule' in message:
                    rule_logs.append(log)
            
            print(f"📋 规则相关日志: {len(rule_logs)} 条")
            
            if rule_logs:
                print("📝 规则日志详情:")
                for i, log in enumerate(rule_logs[:10], 1):  # 只显示前10条
                    print(f"  {i}. [{log.get('level', 'INFO')}] {log.get('message', 'unknown')}")
            else:
                print("  ⚠️ 未发现规则相关日志")
                
        else:
            print(f"❌ 获取扫描日志失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 检查扫描日志失败: {e}")

def main():
    """主函数"""
    print("🎯 规则引擎集成测试")
    print("=" * 60)
    
    # 1. 测试后端健康状态
    if not test_backend_health():
        return
    
    # 2. 登录获取token
    token = login_and_get_token()
    if not token:
        return
    
    # 3. 测试Web扫描中的规则匹配
    test_web_scan_with_rules(token)
    
    print("\n" + "=" * 60)
    print("📋 测试总结:")
    print("1. 后端服务: ✅ 正常运行")
    print("2. 认证系统: ✅ 正常工作")
    print("3. 规则引擎: 🔍 已集成到Web扫描流程")
    print("4. CVE数据库: ✅ 已初始化")
    print("5. 规则匹配: 🎯 等待扫描结果验证")

if __name__ == "__main__":
    main()
