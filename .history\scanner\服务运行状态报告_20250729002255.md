# 漏洞扫描器服务运行状态报告

## 📊 服务状态总结

**检查时间**: 2025年7月29日 00:20

### ✅ 服务运行状态

| 服务 | 状态 | 端口 | 访问地址 | 说明 |
|------|------|------|----------|------|
| 后端服务 | ✅ 运行中 | 8082 | http://localhost:8082 | Go语言后端API服务 |
| 前端服务 | ✅ 运行中 | 3000 | http://localhost:3000 | Vue.js前端应用 |
| 数据库 | ✅ 正常 | - | SQLite本地数据库 | 数据存储服务 |

### 🔍 详细检查结果

#### 后端服务 (Go)
- **进程状态**: 正在运行 (Terminal ID: 46)
- **端口监听**: 8082 (IPv4/IPv6双栈)
- **健康检查**: ✅ 通过 (HTTP 200)
- **响应内容**: `{"message":"漏洞扫描器运行正常","status":"ok","version":"1.0.0"}`
- **启动命令**: `go run cmd/main.go -config configs/app.yaml`

#### 前端服务 (Node.js/Vite)
- **进程状态**: 正在运行 (Terminal ID: 48)
- **端口监听**: 3000
- **开发服务器**: Vite v6.3.5
- **网络访问**:
  - Local: http://localhost:3000/
  - Network: http://*************:3000/
  - Network: http://***********:3000/
- **Vue DevTools**: http://localhost:3000/__devtools__/
- **启动命令**: `npm run dev`

#### 数据库服务 (SQLite)
- **数据库文件**: `data/scanner.db` ✅ 存在
- **连接状态**: ✅ 正常
- **数据迁移**: ✅ 已完成
- **表结构**: ✅ 已初始化

## 🌐 访问地址

### 主要服务
- **前端应用**: http://localhost:3000
- **后端API**: http://localhost:8082
- **健康检查**: http://localhost:8082/health

### 开发工具
- **Vue DevTools**: http://localhost:3000/__devtools__/
- **API文档**: http://localhost:8082/swagger/index.html (如果已配置)

## 🔧 技术栈信息

### 后端技术栈
- **语言**: Go 1.22+
- **框架**: Gin Web Framework
- **数据库**: SQLite 3
- **ORM**: GORM
- **配置**: Viper (YAML)
- **日志**: Logrus + Lumberjack

### 前端技术栈
- **语言**: TypeScript
- **框架**: Vue 3
- **构建工具**: Vite 6.3.5
- **UI组件**: Element Plus
- **状态管理**: Pinia
- **路由**: Vue Router
- **图表**: ECharts

## 📈 性能指标

### 启动时间
- **后端启动**: ~2秒 (包含数据库迁移)
- **前端启动**: ~0.6秒 (Vite热启动)

### 内存使用
- **后端进程**: 约50-100MB (根据扫描任务数量)
- **前端进程**: 约30-50MB (开发模式)

## 🛠️ 运维操作

### 启动服务
```bash
# 方法1: 使用启动脚本 (推荐)
双击 "启动服务.bat"

# 方法2: 手动启动
# 后端
go run cmd/main.go -config configs/app.yaml

# 前端 (新终端)
cd web
npm run dev
```

### 停止服务
- 关闭对应的命令行窗口
- 或在终端中按 `Ctrl+C`

### 检查服务状态
```bash
# 检查端口占用
netstat -an | findstr ":8080"
netstat -an | findstr ":3000"

# 健康检查
curl http://localhost:8080/health
```

## 📝 日志文件

### 后端日志
- **位置**: `logs/scanner.log`
- **格式**: JSON格式
- **轮转**: 100MB/文件，保留5个备份

### 前端日志
- **开发模式**: 控制台输出
- **生产模式**: 浏览器控制台

## ⚠️ 注意事项

1. **端口冲突**: 确保8080和3000端口未被其他程序占用
2. **防火墙**: 如需外部访问，请配置防火墙规则
3. **数据备份**: 定期备份 `data/scanner.db` 数据库文件
4. **日志清理**: 定期清理日志文件避免磁盘空间不足

## 🔄 下次启动

服务已成功启动并运行正常。下次启动时：
1. 可直接双击 `启动服务.bat` 快速启动
2. 或使用 `检查服务状态.bat` 检查当前状态
3. 浏览器会自动打开前端应用页面

---

**报告生成时间**: 2025年7月21日 21:14  
**系统版本**: 漏洞扫描器 v1.0  
**运行环境**: Windows PowerShell
