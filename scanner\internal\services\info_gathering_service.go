package services

import (
	"encoding/json"
	"fmt"
	"time"

	"scanner/internal/models"
	"scanner/pkg/logger"

	"gorm.io/gorm"
)

// InfoGatheringService 信息收集服务
// 专门用于处理扫描过程中收集到的指纹识别、目录发现等信息的存储和查询
type InfoGatheringService struct {
	db *gorm.DB
}

// NewInfoGatheringService 创建新的信息收集服务
func NewInfoGatheringService(db *gorm.DB) *InfoGatheringService {
	return &InfoGatheringService{
		db: db,
	}
}

// LogFingerprint 记录指纹识别信息
// 用于记录"检测到服务器: openresty"、"检测到框架: PHP"等信息
func (s *InfoGatheringService) LogFingerprint(taskID uint, target, category, name, value string, confidence float64, evidence string) error {
	log := &models.InfoGatheringLog{
		TaskID:     taskID,
		Target:     target,
		InfoType:   "fingerprint",
		Category:   category,
		Name:       name,
		Value:      value,
		Confidence: confidence,
		Evidence:   evidence,
		CreatedAt:  time.Now(),
	}

	if err := s.db.Create(log).Error; err != nil {
		return fmt.Errorf("记录指纹识别信息失败: %v", err)
	}

	logger.Infof("记录指纹识别: %s - %s: %s", target, name, value)
	return nil
}

// LogDirectory 记录目录发现信息
// 用于记录"发现可访问目录: /admin [状态码: 200]"等信息
func (s *InfoGatheringService) LogDirectory(taskID uint, target, path string, statusCode int, evidence string) error {
	log := &models.InfoGatheringLog{
		TaskID:     taskID,
		Target:     target,
		InfoType:   "directory",
		Category:   "path_discovery",
		Name:       "accessible_directory",
		Value:      path,
		StatusCode: statusCode,
		Evidence:   evidence,
		CreatedAt:  time.Now(),
	}

	if err := s.db.Create(log).Error; err != nil {
		return fmt.Errorf("记录目录发现信息失败: %v", err)
	}

	logger.Infof("记录目录发现: %s - 路径: %s [状态码: %d]", target, path, statusCode)
	return nil
}

// LogService 记录服务识别信息
// 用于记录端口服务、协议等信息
func (s *InfoGatheringService) LogService(taskID uint, target, serviceName, version string, port int, protocol string, evidence string) error {
	metadata := map[string]interface{}{
		"port":     port,
		"protocol": protocol,
	}
	metadataJSON, _ := json.Marshal(metadata)

	log := &models.InfoGatheringLog{
		TaskID:     taskID,
		Target:     target,
		InfoType:   "service",
		Category:   "service_discovery",
		Name:       serviceName,
		Value:      version,
		Evidence:   evidence,
		Metadata:   string(metadataJSON),
		CreatedAt:  time.Now(),
	}

	if err := s.db.Create(log).Error; err != nil {
		return fmt.Errorf("记录服务识别信息失败: %v", err)
	}

	logger.Infof("记录服务识别: %s - 服务: %s:%d (%s)", target, serviceName, port, protocol)
	return nil
}

// LogTechnology 记录技术栈信息
// 用于记录更详细的技术栈信息
func (s *InfoGatheringService) LogTechnology(taskID uint, target, techType, name, version string, confidence float64, evidence string, metadata map[string]interface{}) error {
	metadataJSON, _ := json.Marshal(metadata)

	log := &models.InfoGatheringLog{
		TaskID:     taskID,
		Target:     target,
		InfoType:   "technology",
		Category:   techType,
		Name:       name,
		Value:      version,
		Confidence: confidence,
		Evidence:   evidence,
		Metadata:   string(metadataJSON),
		CreatedAt:  time.Now(),
	}

	if err := s.db.Create(log).Error; err != nil {
		return fmt.Errorf("记录技术栈信息失败: %v", err)
	}

	logger.Infof("记录技术栈: %s - %s: %s %s (置信度: %.2f)", target, techType, name, version, confidence)
	return nil
}

// GetInfoGatheringLogs 获取信息收集日志
func (s *InfoGatheringService) GetInfoGatheringLogs(taskID uint, infoType string, page, size int) ([]*models.InfoGatheringLog, int64, error) {
	var logs []*models.InfoGatheringLog
	var total int64

	query := s.db.Model(&models.InfoGatheringLog{}).Where("task_id = ?", taskID)

	// 添加信息类型过滤
	if infoType != "" {
		query = query.Where("info_type = ?", infoType)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("获取信息收集日志总数失败: %v", err)
	}

	// 分页查询
	offset := (page - 1) * size
	if err := query.Order("created_at DESC").
		Offset(offset).Limit(size).
		Find(&logs).Error; err != nil {
		return nil, 0, fmt.Errorf("获取信息收集日志失败: %v", err)
	}

	return logs, total, nil
}

// GetFingerprintsByTask 获取任务的指纹识别信息
func (s *InfoGatheringService) GetFingerprintsByTask(taskID uint) ([]*models.InfoGatheringLog, error) {
	var logs []*models.InfoGatheringLog

	if err := s.db.Where("task_id = ? AND info_type = ?", taskID, "fingerprint").
		Order("created_at DESC").
		Find(&logs).Error; err != nil {
		return nil, fmt.Errorf("获取指纹识别信息失败: %v", err)
	}

	return logs, nil
}

// GetDirectoriesByTask 获取任务的目录发现信息
func (s *InfoGatheringService) GetDirectoriesByTask(taskID uint) ([]*models.InfoGatheringLog, error) {
	var logs []*models.InfoGatheringLog

	if err := s.db.Where("task_id = ? AND info_type = ?", taskID, "directory").
		Order("created_at DESC").
		Find(&logs).Error; err != nil {
		return nil, fmt.Errorf("获取目录发现信息失败: %v", err)
	}

	return logs, nil
}

// GetServicesByTask 获取任务的服务识别信息
func (s *InfoGatheringService) GetServicesByTask(taskID uint) ([]*models.InfoGatheringLog, error) {
	var logs []*models.InfoGatheringLog

	if err := s.db.Where("task_id = ? AND info_type = ?", taskID, "service").
		Order("created_at DESC").
		Find(&logs).Error; err != nil {
		return nil, fmt.Errorf("获取服务识别信息失败: %v", err)
	}

	return logs, nil
}

// GetTechnologiesByTask 获取任务的技术栈信息
func (s *InfoGatheringService) GetTechnologiesByTask(taskID uint) ([]*models.InfoGatheringLog, error) {
	var logs []*models.InfoGatheringLog

	if err := s.db.Where("task_id = ? AND info_type = ?", taskID, "technology").
		Order("created_at DESC").
		Find(&logs).Error; err != nil {
		return nil, fmt.Errorf("获取技术栈信息失败: %v", err)
	}

	return logs, nil
}

// DeleteInfoGatheringLogs 删除任务的信息收集日志
func (s *InfoGatheringService) DeleteInfoGatheringLogs(taskID uint) error {
	if err := s.db.Where("task_id = ?", taskID).Delete(&models.InfoGatheringLog{}).Error; err != nil {
		return fmt.Errorf("删除信息收集日志失败: %v", err)
	}

	logger.Infof("已删除任务 %d 的信息收集日志", taskID)
	return nil
}

// GetInfoGatheringStats 获取信息收集统计
func (s *InfoGatheringService) GetInfoGatheringStats(taskID uint) (map[string]int64, error) {
	stats := make(map[string]int64)

	// 统计各类型信息数量
	infoTypes := []string{"fingerprint", "directory", "service", "technology"}
	for _, infoType := range infoTypes {
		var count int64
		if err := s.db.Model(&models.InfoGatheringLog{}).
			Where("task_id = ? AND info_type = ?", taskID, infoType).
			Count(&count).Error; err != nil {
			return nil, fmt.Errorf("获取%s统计失败: %v", infoType, err)
		}
		stats[infoType] = count
	}

	// 总计
	var total int64
	if err := s.db.Model(&models.InfoGatheringLog{}).
		Where("task_id = ?", taskID).
		Count(&total).Error; err != nil {
		return nil, fmt.Errorf("获取总统计失败: %v", err)
	}
	stats["total"] = total

	return stats, nil
}
