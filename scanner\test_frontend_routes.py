#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
前端路由测试脚本
测试前端各个页面是否能正常访问
"""

import requests
import time

# 配置
FRONTEND_URL = "http://localhost:3000"
BACKEND_URL = "http://localhost:8082"

def test_frontend_routes():
    """测试前端路由"""
    
    print("🌐 测试前端路由访问...")
    
    routes = [
        "/",
        "/login", 
        "/scans",
        "/assets",
        "/vulnerabilities",
        "/settings"
    ]
    
    for route in routes:
        try:
            url = f"{FRONTEND_URL}{route}"
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                print(f"✅ {route} - 状态码: {response.status_code}")
            else:
                print(f"❌ {route} - 状态码: {response.status_code}")
                
        except Exception as e:
            print(f"❌ {route} - 错误: {e}")

def test_backend_health():
    """测试后端健康状态"""
    
    print("\n🔧 测试后端服务健康状态...")
    
    try:
        response = requests.get(f"{BACKEND_URL}/health", timeout=10)
        if response.status_code == 200:
            print(f"✅ 后端服务正常 - 状态码: {response.status_code}")
            print(f"   响应: {response.json()}")
        else:
            print(f"❌ 后端服务异常 - 状态码: {response.status_code}")
    except Exception as e:
        print(f"❌ 后端服务连接失败: {e}")

def test_api_endpoints():
    """测试主要API端点"""
    
    print("\n📡 测试主要API端点...")
    
    endpoints = [
        "/api/v1/health",
        "/api/v1/auth/login",
        "/api/v1/scans",
        "/api/v1/assets",
        "/api/v1/vulnerabilities"
    ]
    
    for endpoint in endpoints:
        try:
            url = f"{BACKEND_URL}{endpoint}"
            
            if endpoint == "/api/v1/auth/login":
                # 登录端点需要POST请求
                response = requests.post(url, json={"username": "test", "password": "test"}, timeout=5)
            else:
                response = requests.get(url, timeout=5)
            
            print(f"📋 {endpoint} - 状态码: {response.status_code}")
            
        except Exception as e:
            print(f"❌ {endpoint} - 错误: {e}")

def main():
    """主函数"""
    
    print("🎯 漏洞扫描器前后端服务测试")
    print("=" * 50)
    
    # 测试后端健康状态
    test_backend_health()
    
    # 测试API端点
    test_api_endpoints()
    
    # 测试前端路由
    test_frontend_routes()
    
    print("\n✅ 测试完成！")

if __name__ == "__main__":
    main()
