@echo off
chcp 65001 >nul
title 漏洞扫描器服务启动

echo =================================
echo     漏洞扫描器服务启动
echo =================================
echo.

:: 检查是否在正确目录
if not exist "cmd\main.go" (
    echo 错误: 请在scanner目录下运行此脚本
    pause
    exit /b 1
)

:: 创建必要目录
if not exist "data" mkdir data
if not exist "logs" mkdir logs
if not exist "reports" mkdir reports
if not exist "temp" mkdir temp

echo 正在启动后端服务...
start "后端服务" cmd /k "go run cmd/main.go -config configs/app.yaml"

:: 等待后端启动
timeout /t 3 /nobreak >nul

echo 正在启动前端服务...
start "前端服务" cmd /k "cd web && npm run dev"

:: 等待前端启动
timeout /t 5 /nobreak >nul

echo.
echo =================================
echo     服务启动完成
echo =================================
echo 前端地址: http://localhost:3000
echo 后端地址: http://localhost:8080
echo 健康检查: http://localhost:8080/health
echo.
echo 按任意键打开前端页面...
pause >nul

:: 打开浏览器
start http://localhost:3000

echo.
echo 服务已在后台运行，关闭此窗口不会停止服务
echo 如需停止服务，请关闭对应的命令行窗口
pause
